.content {
    padding-top: 94.5px;
}

.review_home {
    display: flex;
    flex-wrap: wrap;
    width: 90%;
    justify-content: space-between;
    margin: 0 auto;
    gap: 20px;
    margin-top: 40px;
}

.item_review {
    width: 30%;
    min-width: 350px;
    min-height: 400px;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.slider {
    width: 100%;
}

.image_slider {
    width: 100%;
    height: auto;
}

.item_review:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}


.text_itemReview {
    font-size: 36px;
    font-weight: bold;
    color: #fff;
}

.products_home {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 90%;
    margin: 0 auto;
    margin-top: 30px;
    margin-bottom: 30px;
}

.item_products_home {
    width: 20%;
    min-width: 300px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    padding: 10px;
    padding-bottom: 30px;
    border-radius: 10px;
}

.item_products_home:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

.image_products_home {
    width: 100%;
    object-fit: cover;
    height: 100%;
}

.infProducts_home {
    text-align: center;

}

.image_home_item {
    height: 350px;
    display: flex;
    align-items: center;
    overflow: hidden;
    justify-content: center;
}

.title_home_product {
    text-align: center;
    margin-top: 50px;
}

.box_image_poster {
    width: 250px;
    height: 250px;
}

.slick-carousel img {

    transition: transform 0.8s ease;

}

.slick-carousel img:hover {
    transform: scale(1.1);

}

.slick-carousel {
    width: 100%;
    margin: 0 auto;

}

.title_home_poster {
    text-align: center;
    margin-bottom: 20px;
}

.address_contact>a {
    text-decoration: none;
    color: #bbbbbb;
}

.address_contact>a:hover {
    text-decoration: underline;
}