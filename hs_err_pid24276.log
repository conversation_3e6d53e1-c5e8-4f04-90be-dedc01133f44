#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 266338304 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=24276, tid=19972
#
# JRE version:  (17.0.6+9) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\lib\idea_rt.jar=61318:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\bin -Dfile.encoding=UTF-8 com.example.demo.DemoApplication

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Jul 15 11:16:54 2025 SE Asia Standard Time elapsed time: 0.016677 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000167c6cc67b0):  JavaThread "Unknown thread" [_thread_in_vm, id=19972, stack(0x0000000179000000,0x0000000179100000)]

Stack: [0x0000000179000000,0x0000000179100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6734ea]
V  [jvm.dll+0x7d18f4]
V  [jvm.dll+0x7d309e]
V  [jvm.dll+0x7d3703]
V  [jvm.dll+0x2433c5]
V  [jvm.dll+0x6703f9]
V  [jvm.dll+0x664d32]
V  [jvm.dll+0x300086]
V  [jvm.dll+0x307606]
V  [jvm.dll+0x356c1e]
V  [jvm.dll+0x356e4f]
V  [jvm.dll+0x2d72e8]
V  [jvm.dll+0x2d8254]
V  [jvm.dll+0x7a33b1]
V  [jvm.dll+0x3647f1]
V  [jvm.dll+0x782839]
V  [jvm.dll+0x3e757f]
V  [jvm.dll+0x3e9001]
C  [jli.dll+0x5297]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff835f6e958, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x00000167c6d37390 GCTaskThread "GC Thread#0" [stack: 0x0000000179100000,0x0000000179200000] [id=3720]
  0x00000167c6d47e60 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000000179200000,0x0000000179300000] [id=11856]
  0x00000167c6d48f10 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000000179300000,0x0000000179400000] [id=20964]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8357a7f07]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000167c6cc2070] Heap_lock - owner thread: 0x00000167c6cc67b0

Heap address: 0x0000000703600000, size: 4042 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff835b8c759]

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (1 events):
Event: 0.011 Loaded shared library C:\Program Files\Java\jdk-17\bin\java.dll


Dynamic libraries:
0x00007ff7fce70000 - 0x00007ff7fce80000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff897d30000 - 0x00007ff897f47000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8969a0000 - 0x00007ff896a64000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff895060000 - 0x00007ff895432000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff894ea0000 - 0x00007ff894fb1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff88e460000 - 0x00007ff88e478000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff87fbb0000 - 0x00007ff87fbcb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ff895d90000 - 0x00007ff895e41000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff897710000 - 0x00007ff8977b7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff897a70000 - 0x00007ff897b18000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8955b0000 - 0x00007ff8955d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff896a90000 - 0x00007ff896ba4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8959c0000 - 0x00007ff895b71000 	C:\WINDOWS\System32\USER32.dll
0x00007ff895580000 - 0x00007ff8955a6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff87f3b0000 - 0x00007ff87f64b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772\COMCTL32.dll
0x00007ff895d60000 - 0x00007ff895d89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8955e0000 - 0x00007ff895703000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff894fc0000 - 0x00007ff89505a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff88d8f0000 - 0x00007ff88d8fa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff895c90000 - 0x00007ff895cc1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff883f70000 - 0x00007ff883f7c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ff87d680000 - 0x00007ff87d70e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff8354c0000 - 0x00007ff836097000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff897a60000 - 0x00007ff897a68000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff88f1d0000 - 0x00007ff88f1d9000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff88a830000 - 0x00007ff88a864000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff895cd0000 - 0x00007ff895d41000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff893f00000 - 0x00007ff893f18000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff87fc50000 - 0x00007ff87fc5a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff8925a0000 - 0x00007ff8927d3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff896180000 - 0x00007ff896513000 	C:\WINDOWS\System32\combase.dll
0x00007ff895b80000 - 0x00007ff895c57000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff873470000 - 0x00007ff8734a2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff895500000 - 0x00007ff89557b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff87f290000 - 0x00007ff87f29e000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ff87db50000 - 0x00007ff87db75000 	C:\Program Files\Java\jdk-17\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772;C:\Program Files\Java\jdk-17\bin\server

VM Arguments:
jvm_args: -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\lib\idea_rt.jar=61318:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\bin -Dfile.encoding=UTF-8 
java_command: com.example.demo.DemoApplication
java_class_path (initial): C:\Users\<USER>\IdeaProjects\BeeSt0re\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.4.4\spring-boot-starter-web-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.4.4\spring-boot-starter-json-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.3\jackson-datatype-jdk8-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.3\jackson-module-parameter-names-2.18.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.4.4\spring-boot-starter-tomcat-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.39\tomcat-embed-websocket-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.5\spring-web-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.5\spring-beans-6.2.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.14.5\micrometer-observation-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.14.5\micrometer-commons-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.5\spring-webmvc-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.5\spring-context-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.5\spring-expression-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-jasper\10.1.39\tomcat-embed-jasper-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.39\tomcat-embed-core-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\tomcat-annotations-api\10.1.39\tomcat-annotations-api-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embe
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4238344192                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4238344192                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 1 days 17:21 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 16163M (1707M free)
TotalPageFile size 20259M (AvailPageFile size 32M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 59M, peak: 313M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190) for windows-amd64 JRE (17.0.6+9-LTS-190), built on Dec  6 2022 15:53:54 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
