#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1097552 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=19656, tid=22344
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-419be71e4f5fb16ebdcb12564f1e7cbc-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Sep 23 16:10:35 2025 SE Asia Standard Time elapsed time: 1363.512456 seconds (0d 0h 22m 43s)

---------------  T H R E A D  ---------------

Current thread (0x000001c418f21070):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=22344, stack(0x000000772b000000,0x000000772b100000) (1024K)]


Current CompileTask:
C2:1363512 16143       4       java.lang.invoke.MethodHandleImpl::makePairwiseConvertByEditor (617 bytes)

Stack: [0x000000772b000000,0x000000772b100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x3b919c]
V  [jvm.dll+0x385315]
V  [jvm.dll+0x38477a]
V  [jvm.dll+0x248ed0]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001c4128cb9c0, length=52, elements={
0x000001c3b86dfdc0, 0x000001c3b868d8c0, 0x000001c412024830, 0x000001c412028bb0,
0x000001c412029bb0, 0x000001c41202abb0, 0x000001c41202c900, 0x000001c41202f930,
0x000001c412043040, 0x000001c41212ac50, 0x000001c41239dad0, 0x000001c41721e830,
0x000001c417279450, 0x000001c412bbbe20, 0x000001c412bbac90, 0x000001c417746810,
0x000001c417711990, 0x000001c41785dfb0, 0x000001c41785ecd0, 0x000001c41785f9f0,
0x000001c41785d290, 0x000001c41ab72b90, 0x000001c41ab78770, 0x000001c41ab752f0,
0x000001c41ab745d0, 0x000001c41ab72500, 0x000001c41ab717e0, 0x000001c41ab780e0,
0x000001c41ab77a50, 0x000001c41ab78e00, 0x000001c41ab73220, 0x000001c41ab738b0,
0x000001c41ab75980, 0x000001c41ab773c0, 0x000001c41ab71e70, 0x000001c41ab73f40,
0x000001c41b570210, 0x000001c41afd3ca0, 0x000001c41afccd10, 0x000001c41afce750,
0x000001c41afcfb00, 0x000001c41afce0c0, 0x000001c41afd28f0, 0x000001c41afd2f80,
0x000001c41afd4330, 0x000001c41afcede0, 0x000001c41afd2260, 0x000001c41afd1540,
0x000001c41afd0190, 0x000001c418f21070, 0x000001c418f22bb0, 0x000001c41afd0eb0
}

Java Threads: ( => current thread )
  0x000001c3b86dfdc0 JavaThread "main"                              [_thread_blocked, id=13128, stack(0x0000007727600000,0x0000007727700000) (1024K)]
  0x000001c3b868d8c0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=9576, stack(0x0000007727a00000,0x0000007727b00000) (1024K)]
  0x000001c412024830 JavaThread "Finalizer"                  daemon [_thread_blocked, id=15800, stack(0x0000007727b00000,0x0000007727c00000) (1024K)]
  0x000001c412028bb0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=28936, stack(0x0000007727c00000,0x0000007727d00000) (1024K)]
  0x000001c412029bb0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19972, stack(0x0000007727d00000,0x0000007727e00000) (1024K)]
  0x000001c41202abb0 JavaThread "Service Thread"             daemon [_thread_blocked, id=9752, stack(0x0000007727e00000,0x0000007727f00000) (1024K)]
  0x000001c41202c900 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=4700, stack(0x0000007727f00000,0x0000007728000000) (1024K)]
  0x000001c41202f930 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=28136, stack(0x0000007728000000,0x0000007728100000) (1024K)]
  0x000001c412043040 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21904, stack(0x0000007728100000,0x0000007728200000) (1024K)]
  0x000001c41212ac50 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=23476, stack(0x0000007728200000,0x0000007728300000) (1024K)]
  0x000001c41239dad0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15408, stack(0x0000007728500000,0x0000007728600000) (1024K)]
  0x000001c41721e830 JavaThread "Active Thread: Equinox Container: 6389f8d1-0af8-41fe-8e12-ae1baa03fc76"        [_thread_blocked, id=20612, stack(0x0000007728c00000,0x0000007728d00000) (1024K)]
  0x000001c417279450 JavaThread "Refresh Thread: Equinox Container: 6389f8d1-0af8-41fe-8e12-ae1baa03fc76" daemon [_thread_blocked, id=26500, stack(0x0000007728d00000,0x0000007728e00000) (1024K)]
  0x000001c412bbbe20 JavaThread "Framework Event Dispatcher: Equinox Container: 6389f8d1-0af8-41fe-8e12-ae1baa03fc76" daemon [_thread_blocked, id=6200, stack(0x0000007728f00000,0x0000007729000000) (1024K)]
  0x000001c412bbac90 JavaThread "Start Level: Equinox Container: 6389f8d1-0af8-41fe-8e12-ae1baa03fc76" daemon [_thread_blocked, id=18372, stack(0x0000007729000000,0x0000007729100000) (1024K)]
  0x000001c417746810 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=23544, stack(0x0000007729300000,0x0000007729400000) (1024K)]
  0x000001c417711990 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=11292, stack(0x0000007729400000,0x0000007729500000) (1024K)]
  0x000001c41785dfb0 JavaThread "Worker-JM"                         [_thread_blocked, id=14172, stack(0x0000007729600000,0x0000007729700000) (1024K)]
  0x000001c41785ecd0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=27952, stack(0x0000007729700000,0x0000007729800000) (1024K)]
  0x000001c41785f9f0 JavaThread "Java indexing"              daemon [_thread_blocked, id=28232, stack(0x0000007728400000,0x0000007728500000) (1024K)]
  0x000001c41785d290 JavaThread "Thread-2"                   daemon [_thread_in_native, id=11708, stack(0x0000007729e00000,0x0000007729f00000) (1024K)]
  0x000001c41ab72b90 JavaThread "Thread-3"                   daemon [_thread_in_native, id=16496, stack(0x0000007729f00000,0x000000772a000000) (1024K)]
  0x000001c41ab78770 JavaThread "Thread-4"                   daemon [_thread_in_native, id=11360, stack(0x000000772a000000,0x000000772a100000) (1024K)]
  0x000001c41ab752f0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=23852, stack(0x000000772a100000,0x000000772a200000) (1024K)]
  0x000001c41ab745d0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=26172, stack(0x000000772a200000,0x000000772a300000) (1024K)]
  0x000001c41ab72500 JavaThread "Thread-7"                   daemon [_thread_in_native, id=20200, stack(0x000000772a300000,0x000000772a400000) (1024K)]
  0x000001c41ab717e0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=5616, stack(0x000000772a400000,0x000000772a500000) (1024K)]
  0x000001c41ab780e0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=23676, stack(0x000000772a500000,0x000000772a600000) (1024K)]
  0x000001c41ab77a50 JavaThread "Thread-10"                  daemon [_thread_in_native, id=25580, stack(0x000000772a600000,0x000000772a700000) (1024K)]
  0x000001c41ab78e00 JavaThread "Thread-11"                  daemon [_thread_in_native, id=16468, stack(0x000000772a700000,0x000000772a800000) (1024K)]
  0x000001c41ab73220 JavaThread "Thread-12"                  daemon [_thread_in_native, id=7676, stack(0x000000772a800000,0x000000772a900000) (1024K)]
  0x000001c41ab738b0 JavaThread "Thread-13"                  daemon [_thread_in_native, id=30488, stack(0x000000772a900000,0x000000772aa00000) (1024K)]
  0x000001c41ab75980 JavaThread "Thread-14"                  daemon [_thread_in_native, id=28592, stack(0x000000772aa00000,0x000000772ab00000) (1024K)]
  0x000001c41ab773c0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=24124, stack(0x000000772ab00000,0x000000772ac00000) (1024K)]
  0x000001c41ab71e70 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=22244, stack(0x000000772ac00000,0x000000772ad00000) (1024K)]
  0x000001c41ab73f40 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=1312, stack(0x000000772ad00000,0x000000772ae00000) (1024K)]
  0x000001c41b570210 JavaThread "Worker-9"                          [_thread_blocked, id=25996, stack(0x0000007729c00000,0x0000007729d00000) (1024K)]
  0x000001c41afd3ca0 JavaThread "Worker-12"                         [_thread_blocked, id=13088, stack(0x0000007727400000,0x0000007727500000) (1024K)]
  0x000001c41afccd10 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=15324, stack(0x0000007727300000,0x0000007727400000) (1024K)]
  0x000001c41afce750 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=27404, stack(0x0000007727500000,0x0000007727600000) (1024K)]
  0x000001c41afcfb00 JavaThread "ForkJoinPool.commonPool-worker-16" daemon [_thread_in_vm, id=17376, stack(0x0000007728300000,0x0000007728400000) (1024K)]
  0x000001c41afce0c0 JavaThread "Worker-13"                         [_thread_blocked, id=24840, stack(0x0000007729500000,0x0000007729600000) (1024K)]
  0x000001c41afd28f0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=4956, stack(0x0000007729a00000,0x0000007729b00000) (1024K)]
  0x000001c41afd2f80 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=20444, stack(0x0000007729b00000,0x0000007729c00000) (1024K)]
  0x000001c41afd4330 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=16144, stack(0x0000007729900000,0x0000007729a00000) (1024K)]
  0x000001c41afcede0 JavaThread "Worker-14"                         [_thread_blocked, id=10864, stack(0x0000007729d00000,0x0000007729e00000) (1024K)]
  0x000001c41afd2260 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=22088, stack(0x000000772ae00000,0x000000772af00000) (1024K)]
  0x000001c41afd1540 JavaThread "ForkJoinPool.commonPool-worker-17" daemon [_thread_blocked, id=20400, stack(0x0000007729800000,0x0000007729900000) (1024K)]
  0x000001c41afd0190 JavaThread "ForkJoinPool.commonPool-worker-18" daemon [_thread_blocked, id=16776, stack(0x000000772af00000,0x000000772b000000) (1024K)]
=>0x000001c418f21070 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=22344, stack(0x000000772b000000,0x000000772b100000) (1024K)]
  0x000001c418f22bb0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=14428, stack(0x000000772b100000,0x000000772b200000) (1024K)]
  0x000001c41afd0eb0 JavaThread "Cleaner-0"                  daemon [_thread_blocked, id=13912, stack(0x000000772b200000,0x000000772b300000) (1024K)]
Total: 52

Other Threads:
  0x000001c3b8676670 VMThread "VM Thread"                           [id=7548, stack(0x0000007727900000,0x0000007727a00000) (1024K)]
  0x000001c3b874a710 WatcherThread "VM Periodic Task Thread"        [id=26368, stack(0x0000007727800000,0x0000007727900000) (1024K)]
  0x000001c3b86feb00 WorkerThread "GC Thread#0"                     [id=21420, stack(0x0000007727700000,0x0000007727800000) (1024K)]
  0x000001c412b44f90 WorkerThread "GC Thread#1"                     [id=1636, stack(0x0000007728600000,0x0000007728700000) (1024K)]
  0x000001c412b465c0 WorkerThread "GC Thread#2"                     [id=10544, stack(0x0000007728700000,0x0000007728800000) (1024K)]
  0x000001c412b46960 WorkerThread "GC Thread#3"                     [id=15364, stack(0x0000007728800000,0x0000007728900000) (1024K)]
  0x000001c4123fbc30 WorkerThread "GC Thread#4"                     [id=13104, stack(0x0000007728900000,0x0000007728a00000) (1024K)]
  0x000001c4123fbfd0 WorkerThread "GC Thread#5"                     [id=30592, stack(0x0000007728a00000,0x0000007728b00000) (1024K)]
  0x000001c4123fc780 WorkerThread "GC Thread#6"                     [id=15916, stack(0x0000007728b00000,0x0000007728c00000) (1024K)]
  0x000001c4170de750 WorkerThread "GC Thread#7"                     [id=19476, stack(0x0000007728e00000,0x0000007728f00000) (1024K)]
  0x000001c41708ee60 WorkerThread "GC Thread#8"                     [id=12168, stack(0x0000007729100000,0x0000007729200000) (1024K)]
  0x000001c41708f940 WorkerThread "GC Thread#9"                     [id=14584, stack(0x0000007729200000,0x0000007729300000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  1363531 16147 %     4       org.lombokweb.asm.ClassReader::readCode @ 2037 (5117 bytes)
C2 CompilerThread1  1363531 16143       4       java.lang.invoke.MethodHandleImpl::makePairwiseConvertByEditor (617 bytes)
C2 CompilerThread2  1363531 16088   !   4       lombok.core.AST::buildWithField0 (121 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001c3cf000000-0x000001c3cfba0000-0x000001c3cfba0000), size 12189696, SharedBaseAddress: 0x000001c3cf000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001c3d0000000-0x000001c410000000, reserved size: 1073741824
Narrow klass base: 0x000001c3cf000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 13312K, used 12095K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 92% used [0x00000000d5580000,0x00000000d5fb6a60,0x00000000d6080000)
  from space 2048K, 79% used [0x00000000d6280000,0x00000000d6419210,0x00000000d6480000)
  to   space 2048K, 0% used [0x00000000d6080000,0x00000000d6080000,0x00000000d6280000)
 ParOldGen       total 1055744K, used 1055575K [0x0000000080000000, 0x00000000c0700000, 0x00000000d5580000)
  object space 1055744K, 99% used [0x0000000080000000,0x00000000c06d5c90,0x00000000c0700000)
 Metaspace       used 72849K, committed 74432K, reserved 1179648K
  class space    used 7412K, committed 8128K, reserved 1048576K

Card table byte_map: [0x000001c3b8080000,0x000001c3b8490000] _byte_map_base: 0x000001c3b7c80000

Marking Bits: (ParMarkBitMap*) 0x00007ff849d1a340
 Begin Bits: [0x000001c3cab80000, 0x000001c3ccb80000)
 End Bits:   [0x000001c3ccb80000, 0x000001c3ceb80000)

Polling page: 0x000001c3b6320000

Metaspace:

Usage:
  Non-class:     63.90 MB used.
      Class:      7.24 MB used.
       Both:     71.14 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      64.75 MB ( 51%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       7.94 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      72.69 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  15.23 MB
       Class:  7.94 MB
        Both:  23.17 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1418.
num_arena_deaths: 26.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1163.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 40.
num_chunks_taken_from_freelist: 4969.
num_chunk_merges: 17.
num_chunk_splits: 3008.
num_chunks_enlarged: 1690.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=13840Kb max_used=13840Kb free=106159Kb
 bounds [0x000001c3c33a0000, 0x000001c3c4130000, 0x000001c3ca8d0000]
CodeHeap 'profiled nmethods': size=120000Kb used=36030Kb max_used=36030Kb free=83969Kb
 bounds [0x000001c3bb8d0000, 0x000001c3bdc00000, 0x000001c3c2e00000]
CodeHeap 'non-nmethods': size=5760Kb used=1470Kb max_used=1578Kb free=4289Kb
 bounds [0x000001c3c2e00000, 0x000001c3c3070000, 0x000001c3c33a0000]
CodeCache: size=245760Kb, used=51340Kb, max_used=51448Kb, free=194417Kb
 total_blobs=16176, nmethods=15399, adapters=682, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 1363.495 Thread 0x000001c412043040 16210       3       org.eclipse.jdt.internal.compiler.lookup.ReferenceBinding::isNestmateOf (26 bytes)
Event: 1363.495 Thread 0x000001c412043040 nmethod 16210 0x000001c3bdbf5610 code [0x000001c3bdbf57e0, 0x000001c3bdbf5ab0]
Event: 1363.495 Thread 0x000001c412043040 16201       3       org.eclipse.jdt.internal.compiler.ast.QualifiedTypeReference::rejectAnnotationsOnPackageQualifiers (129 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16201 0x000001c3bdbf5c10 code [0x000001c3bdbf5e40, 0x000001c3bdbf6780]
Event: 1363.496 Thread 0x000001c412043040 16212       3       org.eclipse.jdt.internal.compiler.impl.JavaFeature::isSupported (22 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16212 0x000001c3bdbf6b90 code [0x000001c3bdbf6d40, 0x000001c3bdbf6f28]
Event: 1363.496 Thread 0x000001c412043040 16203       3       org.eclipse.jdt.core.dom.Statement::<init> (11 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16203 0x000001c3bdbf7010 code [0x000001c3bdbf71c0, 0x000001c3bdbf7328]
Event: 1363.496 Thread 0x000001c412043040 16209       1       org.eclipse.jdt.internal.compiler.lookup.AnnotationBinding::getElementValuePairs (5 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16209 0x000001c3c4122610 code [0x000001c3c41227a0, 0x000001c3c4122868]
Event: 1363.496 Thread 0x000001c412043040 16197       1       org.eclipse.jdt.core.dom.ExpressionStatement::getNodeType0 (3 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16197 0x000001c3c4122910 code [0x000001c3c4122aa0, 0x000001c3c4122b68]
Event: 1363.496 Thread 0x000001c412043040 16211       1       org.eclipse.jdt.internal.compiler.ast.Argument::isArgument (2 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16211 0x000001c3c4122c10 code [0x000001c3c4122da0, 0x000001c3c4122e68]
Event: 1363.496 Thread 0x000001c412043040 16213       3       lombok.eclipse.handlers.EclipseHandlerUtil::methodExists (8 bytes)
Event: 1363.496 Thread 0x000001c412043040 nmethod 16213 0x000001c3bdbf7410 code [0x000001c3bdbf75c0, 0x000001c3bdbf76e0]
Event: 1363.509 Thread 0x000001c412043040 16215       3       org.eclipse.jdt.internal.compiler.lookup.MethodBinding::setAnnotations (11 bytes)
Event: 1363.509 Thread 0x000001c412043040 nmethod 16215 0x000001c3bdbf7790 code [0x000001c3bdbf7940, 0x000001c3bdbf7b30]
Event: 1363.510 Thread 0x000001c412043040 16216       1       org.eclipse.jdt.internal.compiler.ast.ASTNode::isUnqualifiedSuper (2 bytes)
Event: 1363.510 Thread 0x000001c412043040 nmethod 16216 0x000001c3c4122f10 code [0x000001c3c41230a0, 0x000001c3c4123168]

GC Heap History (20 events):
Event: 1326.366 GC heap before
{Heap before GC invocations=466 (full 3):
 PSYoungGen      total 9728K, used 9221K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000d5580000,0x00000000d5a80000,0x00000000d5a80000)
  from space 4608K, 89% used [0x00000000d6080000,0x00000000d64816f0,0x00000000d6500000)
  to   space 4608K, 0% used [0x00000000d5c00000,0x00000000d5c00000,0x00000000d6080000)
 ParOldGen       total 995840K, used 995395K [0x0000000080000000, 0x00000000bcc80000, 0x00000000d5580000)
  object space 995840K, 99% used [0x0000000080000000,0x00000000bcc10e00,0x00000000bcc80000)
 Metaspace       used 70827K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.369 GC heap after
{Heap after GC invocations=466 (full 3):
 PSYoungGen      total 9728K, used 3234K [0x00000000d5580000, 0x00000000d6380000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 3584K, 90% used [0x00000000d5c00000,0x00000000d5f288c0,0x00000000d5f80000)
  to   space 4096K, 0% used [0x00000000d5f80000,0x00000000d5f80000,0x00000000d6380000)
 ParOldGen       total 999936K, used 999463K [0x0000000080000000, 0x00000000bd080000, 0x00000000d5580000)
  object space 999936K, 99% used [0x0000000080000000,0x00000000bd009cf0,0x00000000bd080000)
 Metaspace       used 70827K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.431 GC heap before
{Heap before GC invocations=467 (full 3):
 PSYoungGen      total 9728K, used 9378K [0x00000000d5580000, 0x00000000d6380000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 3584K, 90% used [0x00000000d5c00000,0x00000000d5f288c0,0x00000000d5f80000)
  to   space 4096K, 0% used [0x00000000d5f80000,0x00000000d5f80000,0x00000000d6380000)
 ParOldGen       total 1031680K, used 1030183K [0x0000000080000000, 0x00000000bef80000, 0x00000000d5580000)
  object space 1031680K, 99% used [0x0000000080000000,0x00000000bee09d20,0x00000000bef80000)
 Metaspace       used 70846K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.436 GC heap after
{Heap after GC invocations=467 (full 3):
 PSYoungGen      total 10240K, used 3700K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 4096K, 90% used [0x00000000d5f80000,0x00000000d631d000,0x00000000d6380000)
  to   space 4096K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5f80000)
 ParOldGen       total 1034752K, used 1034275K [0x0000000080000000, 0x00000000bf280000, 0x00000000d5580000)
  object space 1034752K, 99% used [0x0000000080000000,0x00000000bf208d20,0x00000000bf280000)
 Metaspace       used 70846K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.446 GC heap before
{Heap before GC invocations=468 (full 3):
 PSYoungGen      total 10240K, used 9844K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 4096K, 90% used [0x00000000d5f80000,0x00000000d631d000,0x00000000d6380000)
  to   space 4096K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5f80000)
 ParOldGen       total 1034752K, used 1034275K [0x0000000080000000, 0x00000000bf280000, 0x00000000d5580000)
  object space 1034752K, 99% used [0x0000000080000000,0x00000000bf208d20,0x00000000bf280000)
 Metaspace       used 70846K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.452 GC heap after
{Heap after GC invocations=468 (full 3):
 PSYoungGen      total 10240K, used 4084K [0x00000000d5580000, 0x00000000d7100000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 4096K, 99% used [0x00000000d5b80000,0x00000000d5f7d020,0x00000000d5f80000)
  to   space 10240K, 0% used [0x00000000d6700000,0x00000000d6700000,0x00000000d7100000)
 ParOldGen       total 1039360K, used 1039235K [0x0000000080000000, 0x00000000bf700000, 0x00000000d5580000)
  object space 1039360K, 99% used [0x0000000080000000,0x00000000bf6e0d30,0x00000000bf700000)
 Metaspace       used 70846K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.463 GC heap before
{Heap before GC invocations=469 (full 3):
 PSYoungGen      total 10240K, used 10228K [0x00000000d5580000, 0x00000000d7100000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 4096K, 99% used [0x00000000d5b80000,0x00000000d5f7d020,0x00000000d5f80000)
  to   space 10240K, 0% used [0x00000000d6700000,0x00000000d6700000,0x00000000d7100000)
 ParOldGen       total 1039360K, used 1039235K [0x0000000080000000, 0x00000000bf700000, 0x00000000d5580000)
  object space 1039360K, 99% used [0x0000000080000000,0x00000000bf6e0d30,0x00000000bf700000)
 Metaspace       used 70855K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.468 GC heap after
{Heap after GC invocations=469 (full 3):
 PSYoungGen      total 13312K, used 5556K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5d00000)
  from space 5632K, 98% used [0x00000000d6700000,0x00000000d6c6d000,0x00000000d6c80000)
  to   space 7168K, 0% used [0x00000000d5e80000,0x00000000d5e80000,0x00000000d6580000)
 ParOldGen       total 1044480K, used 1044227K [0x0000000080000000, 0x00000000bfc00000, 0x00000000d5580000)
  object space 1044480K, 99% used [0x0000000080000000,0x00000000bfbc0d70,0x00000000bfc00000)
 Metaspace       used 70855K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.555 GC heap before
{Heap before GC invocations=470 (full 3):
 PSYoungGen      total 13312K, used 13236K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000d5580000,0x00000000d5d00000,0x00000000d5d00000)
  from space 5632K, 98% used [0x00000000d6700000,0x00000000d6c6d000,0x00000000d6c80000)
  to   space 7168K, 0% used [0x00000000d5e80000,0x00000000d5e80000,0x00000000d6580000)
 ParOldGen       total 1044480K, used 1044227K [0x0000000080000000, 0x00000000bfc00000, 0x00000000d5580000)
  object space 1044480K, 99% used [0x0000000080000000,0x00000000bfbc0d70,0x00000000bfc00000)
 Metaspace       used 70856K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.561 GC heap after
{Heap after GC invocations=470 (full 3):
 PSYoungGen      total 12800K, used 3396K [0x00000000d5580000, 0x00000000d6780000, 0x0000000100000000)
  eden space 9216K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e80000)
  from space 3584K, 94% used [0x00000000d5e80000,0x00000000d61d1210,0x00000000d6200000)
  to   space 4608K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6780000)
 ParOldGen       total 1050112K, used 1049619K [0x0000000080000000, 0x00000000c0180000, 0x00000000d5580000)
  object space 1050112K, 99% used [0x0000000080000000,0x00000000c0104d70,0x00000000c0180000)
 Metaspace       used 70856K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.580 GC heap before
{Heap before GC invocations=471 (full 3):
 PSYoungGen      total 12800K, used 12612K [0x00000000d5580000, 0x00000000d6780000, 0x0000000100000000)
  eden space 9216K, 100% used [0x00000000d5580000,0x00000000d5e80000,0x00000000d5e80000)
  from space 3584K, 94% used [0x00000000d5e80000,0x00000000d61d1210,0x00000000d6200000)
  to   space 4608K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6780000)
 ParOldGen       total 1050112K, used 1049619K [0x0000000080000000, 0x00000000c0180000, 0x00000000d5580000)
  object space 1050112K, 99% used [0x0000000080000000,0x00000000c0104d70,0x00000000c0180000)
 Metaspace       used 70858K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1326.584 GC heap after
{Heap after GC invocations=471 (full 3):
 PSYoungGen      total 12800K, used 762K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6080000)
  from space 1536K, 49% used [0x00000000d6300000,0x00000000d63be900,0x00000000d6480000)
  to   space 2048K, 0% used [0x00000000d6080000,0x00000000d6080000,0x00000000d6280000)
 ParOldGen       total 1052672K, used 1052534K [0x0000000080000000, 0x00000000c0400000, 0x00000000d5580000)
  object space 1052672K, 99% used [0x0000000080000000,0x00000000c03dda80,0x00000000c0400000)
 Metaspace       used 70858K, committed 72448K, reserved 1114112K
  class space    used 7251K, committed 8000K, reserved 1048576K
}
Event: 1363.118 GC heap before
{Heap before GC invocations=472 (full 3):
 PSYoungGen      total 12800K, used 12026K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000d5580000,0x00000000d6080000,0x00000000d6080000)
  from space 1536K, 49% used [0x00000000d6300000,0x00000000d63be900,0x00000000d6480000)
  to   space 2048K, 0% used [0x00000000d6080000,0x00000000d6080000,0x00000000d6280000)
 ParOldGen       total 1052672K, used 1052534K [0x0000000080000000, 0x00000000c0400000, 0x00000000d5580000)
  object space 1052672K, 99% used [0x0000000080000000,0x00000000c03dda80,0x00000000c0400000)
 Metaspace       used 71476K, committed 73088K, reserved 1114112K
  class space    used 7308K, committed 8064K, reserved 1048576K
}
Event: 1363.120 GC heap after
{Heap after GC invocations=472 (full 3):
 PSYoungGen      total 13312K, used 575K [0x00000000d5580000, 0x00000000d6380000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6080000)
  from space 2048K, 28% used [0x00000000d6080000,0x00000000d610fc30,0x00000000d6280000)
  to   space 1024K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6380000)
 ParOldGen       total 1052672K, used 1052542K [0x0000000080000000, 0x00000000c0400000, 0x00000000d5580000)
  object space 1052672K, 99% used [0x0000000080000000,0x00000000c03dfa80,0x00000000c0400000)
 Metaspace       used 71476K, committed 73088K, reserved 1114112K
  class space    used 7308K, committed 8064K, reserved 1048576K
}
Event: 1363.227 GC heap before
{Heap before GC invocations=473 (full 3):
 PSYoungGen      total 13312K, used 11839K [0x00000000d5580000, 0x00000000d6380000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000d5580000,0x00000000d6080000,0x00000000d6080000)
  from space 2048K, 28% used [0x00000000d6080000,0x00000000d610fc30,0x00000000d6280000)
  to   space 1024K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6380000)
 ParOldGen       total 1052672K, used 1052542K [0x0000000080000000, 0x00000000c0400000, 0x00000000d5580000)
  object space 1052672K, 99% used [0x0000000080000000,0x00000000c03dfa80,0x00000000c0400000)
 Metaspace       used 71897K, committed 73472K, reserved 1114112K
  class space    used 7337K, committed 8064K, reserved 1048576K
}
Event: 1363.231 GC heap after
{Heap after GC invocations=473 (full 3):
 PSYoungGen      total 12288K, used 992K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6080000)
  from space 1024K, 96% used [0x00000000d6280000,0x00000000d6378020,0x00000000d6380000)
  to   space 2048K, 0% used [0x00000000d6080000,0x00000000d6080000,0x00000000d6280000)
 ParOldGen       total 1053696K, used 1053211K [0x0000000080000000, 0x00000000c0500000, 0x00000000d5580000)
  object space 1053696K, 99% used [0x0000000080000000,0x00000000c0486f28,0x00000000c0500000)
 Metaspace       used 71897K, committed 73472K, reserved 1114112K
  class space    used 7337K, committed 8064K, reserved 1048576K
}
Event: 1363.438 GC heap before
{Heap before GC invocations=474 (full 3):
 PSYoungGen      total 12288K, used 12256K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000d5580000,0x00000000d6080000,0x00000000d6080000)
  from space 1024K, 96% used [0x00000000d6280000,0x00000000d6378020,0x00000000d6380000)
  to   space 2048K, 0% used [0x00000000d6080000,0x00000000d6080000,0x00000000d6280000)
 ParOldGen       total 1053696K, used 1053211K [0x0000000080000000, 0x00000000c0500000, 0x00000000d5580000)
  object space 1053696K, 99% used [0x0000000080000000,0x00000000c0486f28,0x00000000c0500000)
 Metaspace       used 72421K, committed 73984K, reserved 1179648K
  class space    used 7378K, committed 8128K, reserved 1048576K
}
Event: 1363.443 GC heap after
{Heap after GC invocations=474 (full 3):
 PSYoungGen      total 13312K, used 1477K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6080000)
  from space 2048K, 72% used [0x00000000d6080000,0x00000000d61f1588,0x00000000d6280000)
  to   space 2048K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6480000)
 ParOldGen       total 1054720K, used 1054223K [0x0000000080000000, 0x00000000c0600000, 0x00000000d5580000)
  object space 1054720K, 99% used [0x0000000080000000,0x00000000c0583f98,0x00000000c0600000)
 Metaspace       used 72421K, committed 73984K, reserved 1179648K
  class space    used 7378K, committed 8128K, reserved 1048576K
}
Event: 1363.473 GC heap before
{Heap before GC invocations=475 (full 3):
 PSYoungGen      total 13312K, used 12741K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 100% used [0x00000000d5580000,0x00000000d6080000,0x00000000d6080000)
  from space 2048K, 72% used [0x00000000d6080000,0x00000000d61f1588,0x00000000d6280000)
  to   space 2048K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6480000)
 ParOldGen       total 1054720K, used 1054223K [0x0000000080000000, 0x00000000c0600000, 0x00000000d5580000)
  object space 1054720K, 99% used [0x0000000080000000,0x00000000c0583f98,0x00000000c0600000)
 Metaspace       used 72453K, committed 74048K, reserved 1179648K
  class space    used 7378K, committed 8128K, reserved 1048576K
}
Event: 1363.477 GC heap after
{Heap after GC invocations=475 (full 3):
 PSYoungGen      total 13312K, used 1636K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11264K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6080000)
  from space 2048K, 79% used [0x00000000d6280000,0x00000000d6419210,0x00000000d6480000)
  to   space 2048K, 0% used [0x00000000d6080000,0x00000000d6080000,0x00000000d6280000)
 ParOldGen       total 1055744K, used 1055575K [0x0000000080000000, 0x00000000c0700000, 0x00000000d5580000)
  object space 1055744K, 99% used [0x0000000080000000,0x00000000c06d5c90,0x00000000c0700000)
 Metaspace       used 72453K, committed 74048K, reserved 1179648K
  class space    used 7378K, committed 8128K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.087 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.128 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.133 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.135 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.139 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.155 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.218 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 1.285 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 2.626 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna6602663923752146412.dll
Event: 777.884 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 1363.308 Thread 0x000001c41afce750 DEOPT PACKING pc=0x000001c3c3a5b184 sp=0x00000077275fdad0
Event: 1363.308 Thread 0x000001c41afce750 DEOPT UNPACKING pc=0x000001c3c2e56da2 sp=0x00000077275fd9a0 mode 2
Event: 1363.310 Thread 0x000001c41afce750 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001c3c39fa9f0 relative=0x00000000000003b0
Event: 1363.310 Thread 0x000001c41afce750 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001c3c39fa9f0 method=java.io.BufferedOutputStream.implWrite([BII)V @ 5 c2
Event: 1363.310 Thread 0x000001c41afce750 DEOPT PACKING pc=0x000001c3c39fa9f0 sp=0x00000077275fdb20
Event: 1363.310 Thread 0x000001c41afce750 DEOPT UNPACKING pc=0x000001c3c2e56da2 sp=0x00000077275fda70 mode 2
Event: 1363.346 Thread 0x000001c41afcfb00 DEOPT PACKING pc=0x000001c3bc23e9c8 sp=0x00000077283fd680
Event: 1363.346 Thread 0x000001c41afcfb00 DEOPT UNPACKING pc=0x000001c3c2e578e2 sp=0x00000077283fcb38 mode 0
Event: 1363.404 Thread 0x000001c41afce750 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001c3c3f73b20 relative=0x0000000000000100
Event: 1363.404 Thread 0x000001c41afce750 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001c3c3f73b20 method=org.eclipse.jdt.internal.compiler.util.Util.getLineNumber(I[III)I @ 8 c2
Event: 1363.404 Thread 0x000001c41afce750 DEOPT PACKING pc=0x000001c3c3f73b20 sp=0x00000077275fef70
Event: 1363.404 Thread 0x000001c41afce750 DEOPT UNPACKING pc=0x000001c3c2e56da2 sp=0x00000077275fef18 mode 2
Event: 1363.404 Thread 0x000001c41afce750 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001c3c3de7d30 relative=0x00000000000006d0
Event: 1363.404 Thread 0x000001c41afce750 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001c3c3de7d30 method=org.eclipse.jdt.internal.compiler.parser.Scanner.getNextToken0()Lorg/eclipse/jdt/internal/compiler/parser/TerminalToken; @ 236 c2
Event: 1363.404 Thread 0x000001c41afce750 DEOPT PACKING pc=0x000001c3c3de7d30 sp=0x00000077275fef40
Event: 1363.404 Thread 0x000001c41afce750 DEOPT UNPACKING pc=0x000001c3c2e56da2 sp=0x00000077275fee70 mode 2
Event: 1363.404 Thread 0x000001c41afce750 Uncommon trap: trap_request=0xffffffe4 fr.pc=0x000001c3c3eabaec relative=0x000000000000030c
Event: 1363.404 Thread 0x000001c41afce750 Uncommon trap: reason=range_check action=make_not_entrant pc=0x000001c3c3eabaec method=org.eclipse.jdt.internal.compiler.parser.Scanner.pushLineSeparator()V @ 104 c2
Event: 1363.404 Thread 0x000001c41afce750 DEOPT PACKING pc=0x000001c3c3eabaec sp=0x00000077275fee40
Event: 1363.404 Thread 0x000001c41afce750 DEOPT UNPACKING pc=0x000001c3c2e56da2 sp=0x00000077275fede8 mode 2

Classes loaded (20 events):
Event: 958.477 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 958.477 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done
Event: 967.875 Loading class jdk/internal/module/IllegalAccessLogger
Event: 967.875 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 981.368 Loading class jdk/internal/module/IllegalAccessLogger
Event: 981.368 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 990.237 Loading class jdk/internal/module/IllegalAccessLogger
Event: 990.237 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1006.208 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1006.208 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1215.808 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1215.808 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1300.227 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1300.227 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1312.923 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1312.923 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1326.360 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1326.360 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1363.406 Loading class jdk/internal/ref/CleanerImpl$InnocuousThreadFactory
Event: 1363.407 Loading class jdk/internal/ref/CleanerImpl$InnocuousThreadFactory done

Classes unloaded (13 events):
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01ac800 'java/lang/invoke/LambdaForm$MH+0x000001c3d01ac800'
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01ac400 'java/lang/invoke/LambdaForm$MH+0x000001c3d01ac400'
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01ac000 'java/lang/invoke/LambdaForm$MH+0x000001c3d01ac000'
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01abc00 'java/lang/invoke/LambdaForm$MH+0x000001c3d01abc00'
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01ab800 'java/lang/invoke/LambdaForm$BMH+0x000001c3d01ab800'
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01ab400 'java/lang/invoke/LambdaForm$DMH+0x000001c3d01ab400'
Event: 3.235 Thread 0x000001c3b8676670 Unloading class 0x000001c3d01aa000 'java/lang/invoke/LambdaForm$DMH+0x000001c3d01aa000'
Event: 10.627 Thread 0x000001c3b8676670 Unloading class 0x000001c3d06c0c00 'java/lang/invoke/LambdaForm$MH+0x000001c3d06c0c00'
Event: 10.627 Thread 0x000001c3b8676670 Unloading class 0x000001c3d06c0000 'java/lang/invoke/LambdaForm$MH+0x000001c3d06c0000'
Event: 10.627 Thread 0x000001c3b8676670 Unloading class 0x000001c3d05ed000 'java/lang/invoke/LambdaForm$DMH+0x000001c3d05ed000'
Event: 10.627 Thread 0x000001c3b8676670 Unloading class 0x000001c3d05ecc00 'java/lang/invoke/LambdaForm$DMH+0x000001c3d05ecc00'
Event: 10.627 Thread 0x000001c3b8676670 Unloading class 0x000001c3d05ec800 'java/lang/invoke/LambdaForm$DMH+0x000001c3d05ec800'
Event: 10.627 Thread 0x000001c3b8676670 Unloading class 0x000001c3d05ec400 'java/lang/invoke/LambdaForm$DMH+0x000001c3d05ec400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 990.384 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a622b0}> (0x00000000d5a622b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1006.038 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5590a28}> (0x00000000d5590a28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1006.350 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d573c8b0}> (0x00000000d573c8b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1071.210 Thread 0x000001c41b570210 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5690cc8}> (0x00000000d5690cc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1071.210 Thread 0x000001c41b570210 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5691800}> (0x00000000d5691800) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1215.653 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5b6af20}> (0x00000000d5b6af20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1215.957 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a35830}> (0x00000000d5a35830) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1300.067 Thread 0x000001c41785f9f0 Implicit null exception at 0x000001c3c3e8f702 to 0x000001c3c3e91a00
Event: 1300.092 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5633c58}> (0x00000000d5633c58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1300.380 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55b1180}> (0x00000000d55b1180) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1312.781 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5bfc098}> (0x00000000d5bfc098) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1313.068 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d577a2a8}> (0x00000000d577a2a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1326.246 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5658428}> (0x00000000d5658428) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1326.527 Thread 0x000001c41785f9f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5961460}> (0x00000000d5961460) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1363.052 Thread 0x000001c41ab73f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5a0f6f8}: Found class java.lang.Object, but interface was expected> (0x00000000d5a0f6f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 1363.103 Thread 0x000001c41ab73f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5d5c330}: Found class java.lang.Object, but interface was expected> (0x00000000d5d5c330) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 1363.111 Thread 0x000001c41afd1540 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e22e88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5e22e88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1363.167 Thread 0x000001c41ab73f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5d58ba8}: Found class java.lang.Object, but interface was expected> (0x00000000d5d58ba8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 1363.398 Thread 0x000001c41ab73f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5c65470}: Found class java.lang.Object, but interface was expected> (0x00000000d5c65470) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 1363.404 Thread 0x000001c41afce750 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'{0x00000000d5cbd318}: Index 695 out of bounds for length 694> (0x00000000d5cbd318) 
thrown [s\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 439]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 1326.431 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1326.436 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1326.446 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1326.452 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1326.463 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1326.468 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1326.555 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1326.561 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1326.580 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1326.584 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1327.585 Executing VM operation: Cleanup
Event: 1327.585 Executing VM operation: Cleanup done
Event: 1363.118 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1363.120 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1363.227 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1363.231 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1363.438 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1363.443 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1363.473 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1363.477 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc14e310
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc14e910
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc14f890
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc151990
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc15ad90
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc174210
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1b1b10
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1cca10
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1ddf10
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1ed010
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1eda10
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1ef310
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1fa910
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc1fe590
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc201390
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc201b10
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc201e90
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc202210
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc223a90
Event: 10.647 Thread 0x000001c3b8676670 flushing  nmethod 0x000001c3bc228410

Events (20 events):
Event: 1300.228 Thread 0x000001c41b570210 Thread added: 0x000001c41afd2f80
Event: 1300.529 Thread 0x000001c418f21070 Thread exited: 0x000001c418f21070
Event: 1300.533 Thread 0x000001c418f1ee60 Thread exited: 0x000001c418f1ee60
Event: 1300.544 Thread 0x000001c412043040 Thread added: 0x000001c418f1d9f0
Event: 1300.557 Thread 0x000001c412043040 Thread added: 0x000001c418f1e790
Event: 1305.615 Thread 0x000001c418f1e790 Thread exited: 0x000001c418f1e790
Event: 1305.646 Thread 0x000001c418f1d9f0 Thread exited: 0x000001c418f1d9f0
Event: 1306.155 Thread 0x000001c41afd2260 Thread exited: 0x000001c41afd2260
Event: 1312.853 Thread 0x000001c412043040 Thread added: 0x000001c418f21070
Event: 1312.880 Thread 0x000001c41afd3ca0 Thread added: 0x000001c41afd4330
Event: 1313.152 Thread 0x000001c41afce0c0 Thread added: 0x000001c41afcede0
Event: 1315.383 Thread 0x000001c418f21070 Thread exited: 0x000001c418f21070
Event: 1326.165 Thread 0x000001c412043040 Thread added: 0x000001c418f1d9f0
Event: 1326.334 Thread 0x000001c41afce0c0 Thread added: 0x000001c41afd2260
Event: 1331.107 Thread 0x000001c418f1d9f0 Thread exited: 0x000001c418f1d9f0
Event: 1363.108 Thread 0x000001c41ab73f40 Thread added: 0x000001c41afd1540
Event: 1363.109 Thread 0x000001c41afcfb00 Thread added: 0x000001c41afd0190
Event: 1363.121 Thread 0x000001c41202f930 Thread added: 0x000001c418f21070
Event: 1363.154 Thread 0x000001c412043040 Thread added: 0x000001c418f22bb0
Event: 1363.407 Thread 0x000001c41afcfb00 Thread added: 0x000001c41afd0eb0


Dynamic libraries:
0x00007ff7011e0000 - 0x00007ff7011ee000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ff8d4bd0000 - 0x00007ff8d4de7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8d2d90000 - 0x00007ff8d2e54000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8d20d0000 - 0x00007ff8d24a0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8d2740000 - 0x00007ff8d2851000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8b7a20000 - 0x00007ff8b7a3e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8b8bf0000 - 0x00007ff8b8c08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ff8d4560000 - 0x00007ff8d4711000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8baf50000 - 0x00007ff8bb1ec000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ff8d2520000 - 0x00007ff8d2546000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8d2860000 - 0x00007ff8d2889000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8d3b90000 - 0x00007ff8d3c37000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8d1d90000 - 0x00007ff8d1eb3000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8d2030000 - 0x00007ff8d20ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8d42c0000 - 0x00007ff8d42f1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c97e0000 - 0x00007ff8c97ec000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff88ef90000 - 0x00007ff88f01d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ff849060000 - 0x00007ff849df7000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ff8d2890000 - 0x00007ff8d2941000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8d4300000 - 0x00007ff8d43a8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8d1ec0000 - 0x00007ff8d1ee8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8d2c00000 - 0x00007ff8d2d18000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8d47d0000 - 0x00007ff8d4841000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8d0ab0000 - 0x00007ff8d0afd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8ca250000 - 0x00007ff8ca25a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8c86a0000 - 0x00007ff8c86d4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8d0a90000 - 0x00007ff8d0aa3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8d0cf0000 - 0x00007ff8d0d08000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c26d0000 - 0x00007ff8c26da000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ff8cf180000 - 0x00007ff8cf3b3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8d3f20000 - 0x00007ff8d42b1000 	C:\WINDOWS\System32\combase.dll
0x00007ff8d2950000 - 0x00007ff8d2a28000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8b8c50000 - 0x00007ff8b8c82000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8d26c0000 - 0x00007ff8d273b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8c1af0000 - 0x00007ff8c1aff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ff8b6480000 - 0x00007ff8b649f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ff8d2e60000 - 0x00007ff8d3701000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8d1ef0000 - 0x00007ff8d202f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff8cfb60000 - 0x00007ff8d047a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8d48c0000 - 0x00007ff8d49ca000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8d3c40000 - 0x00007ff8d3ca9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8d1c00000 - 0x00007ff8d1c25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8b6450000 - 0x00007ff8b6468000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ff8bb850000 - 0x00007ff8bb860000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ff8c9660000 - 0x00007ff8c978c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8d1230000 - 0x00007ff8d1299000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8b6380000 - 0x00007ff8b6396000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ff8bae10000 - 0x00007ff8bae20000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ff8b64a0000 - 0x00007ff8b64e4000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ff8d43b0000 - 0x00007ff8d4550000 	C:\WINDOWS\System32\ole32.dll
0x00007ff8d14c0000 - 0x00007ff8d14db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8d0cb0000 - 0x00007ff8d0ce7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff8d1340000 - 0x00007ff8d1368000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8d14e0000 - 0x00007ff8d14ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8d0750000 - 0x00007ff8d077d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff8d3f10000 - 0x00007ff8d3f19000 	C:\WINDOWS\System32\NSI.dll
0x00007ff8b60a0000 - 0x00007ff8b60e9000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna6602663923752146412.dll
0x00007ff8d4550000 - 0x00007ff8d4558000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8c97c0000 - 0x00007ff8c97d9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff8c9380000 - 0x00007ff8c939f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff8c0210000 - 0x00007ff8c021e000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll
0x00007ff8d2550000 - 0x00007ff8d26b8000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff8d1660000 - 0x00007ff8d168d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff8d1620000 - 0x00007ff8d1657000 	C:\WINDOWS\SYSTEM32\NTASN1.dll

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-419be71e4f5fb16ebdcb12564f1e7cbc-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 9 days 23:19 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (2574M free)
TotalPageFile size 20259M (AvailPageFile size 10M)
current process WorkingSet (physical memory assigned to process): 1348M, peak: 1348M
current process commit charge ("private bytes"): 1420M, peak: 1421M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
