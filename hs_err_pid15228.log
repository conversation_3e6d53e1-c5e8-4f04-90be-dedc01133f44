#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=15228, tid=8828
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-40e5ef75053067ae62606d9243dc3bb7-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Sep 23 21:50:30 2025 SE Asia Standard Time elapsed time: 1803.465513 seconds (0d 0h 30m 3s)

---------------  T H R E A D  ---------------

Current thread (0x000002273ad37520):  JavaThread "Attach Listener" daemon [_thread_in_vm, id=8828, stack(0x0000003f9b300000,0x0000003f9b400000) (1024K)]

Stack: [0x0000003f9b300000,0x0000003f9b400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc0347]
V  [jvm.dll+0x6d2cf9]
V  [jvm.dll+0x131116]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002279a7c4dc0, length=39, elements={
0x000002273ac90dd0, 0x000002273ad2ff60, 0x000002273ad30dc0, 0x000002273ad34680,
0x000002273ad37520, 0x000002273ad39b00, 0x000002273ad3ae40, 0x000002273ad41fd0,
0x000002273ad46240, 0x0000022793b85ed0, 0x0000022793ddafb0, 0x00000227998acc10,
0x0000022799955ad0, 0x0000022793e658d0, 0x0000022799972050, 0x000002279920ef00,
0x000002279977d8a0, 0x0000022799b93e60, 0x0000022799b958a0, 0x0000022799b93140,
0x000002279c533390, 0x000002279c52e4d0, 0x000002279c534dd0, 0x000002279c52eb60,
0x000002279c5312c0, 0x000002279c532d00, 0x000002279c5340b0, 0x000002279c531950,
0x000002279c52de40, 0x000002279c52f1f0, 0x000002279c52ff10, 0x000002279c52f880,
0x000002279c5305a0, 0x000002279c533a20, 0x000002279c530c30, 0x000002279c534740,
0x00000227a7a418e0, 0x000002279b9e4350, 0x00000227a7a40530
}

Java Threads: ( => current thread )
  0x000002273ac90dd0 JavaThread "main"                              [_thread_blocked, id=6544, stack(0x0000003f9ac00000,0x0000003f9ad00000) (1024K)]
  0x000002273ad2ff60 JavaThread "Reference Handler"          daemon [_thread_blocked, id=6224, stack(0x0000003f9b000000,0x0000003f9b100000) (1024K)]
  0x000002273ad30dc0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=23912, stack(0x0000003f9b100000,0x0000003f9b200000) (1024K)]
  0x000002273ad34680 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=26496, stack(0x0000003f9b200000,0x0000003f9b300000) (1024K)]
=>0x000002273ad37520 JavaThread "Attach Listener"            daemon [_thread_in_vm, id=8828, stack(0x0000003f9b300000,0x0000003f9b400000) (1024K)]
  0x000002273ad39b00 JavaThread "Service Thread"             daemon [_thread_blocked, id=15600, stack(0x0000003f9b400000,0x0000003f9b500000) (1024K)]
  0x000002273ad3ae40 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=26852, stack(0x0000003f9b500000,0x0000003f9b600000) (1024K)]
  0x000002273ad41fd0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=9704, stack(0x0000003f9b600000,0x0000003f9b700000) (1024K)]
  0x000002273ad46240 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=1848, stack(0x0000003f9b700000,0x0000003f9b800000) (1024K)]
  0x0000022793b85ed0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=19460, stack(0x0000003f9b800000,0x0000003f9b900000) (1024K)]
  0x0000022793ddafb0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=17996, stack(0x0000003f9ba00000,0x0000003f9bb00000) (1024K)]
  0x00000227998acc10 JavaThread "Active Thread: Equinox Container: 1b8ec796-1d0f-4eb0-a4db-d77e3471936b"        [_thread_blocked, id=4044, stack(0x0000003f9c200000,0x0000003f9c300000) (1024K)]
  0x0000022799955ad0 JavaThread "Refresh Thread: Equinox Container: 1b8ec796-1d0f-4eb0-a4db-d77e3471936b" daemon [_thread_blocked, id=23092, stack(0x0000003f9bb00000,0x0000003f9bc00000) (1024K)]
  0x0000022793e658d0 JavaThread "Framework Event Dispatcher: Equinox Container: 1b8ec796-1d0f-4eb0-a4db-d77e3471936b" daemon [_thread_blocked, id=27764, stack(0x0000003f9c300000,0x0000003f9c400000) (1024K)]
  0x0000022799972050 JavaThread "Start Level: Equinox Container: 1b8ec796-1d0f-4eb0-a4db-d77e3471936b" daemon [_thread_blocked, id=21692, stack(0x0000003f9c400000,0x0000003f9c500000) (1024K)]
  0x000002279920ef00 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=27620, stack(0x0000003f9c800000,0x0000003f9c900000) (1024K)]
  0x000002279977d8a0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=21468, stack(0x0000003f9c500000,0x0000003f9c600000) (1024K)]
  0x0000022799b93e60 JavaThread "Worker-JM"                         [_thread_blocked, id=22284, stack(0x0000003f9cb00000,0x0000003f9cc00000) (1024K)]
  0x0000022799b958a0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=13648, stack(0x0000003f9cd00000,0x0000003f9ce00000) (1024K)]
  0x0000022799b93140 JavaThread "Java indexing"              daemon [_thread_blocked, id=16152, stack(0x0000003f9d100000,0x0000003f9d200000) (1024K)]
  0x000002279c533390 JavaThread "Thread-2"                   daemon [_thread_in_native, id=29612, stack(0x0000003f9d500000,0x0000003f9d600000) (1024K)]
  0x000002279c52e4d0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=2396, stack(0x0000003f9d600000,0x0000003f9d700000) (1024K)]
  0x000002279c534dd0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=7544, stack(0x0000003f9d700000,0x0000003f9d800000) (1024K)]
  0x000002279c52eb60 JavaThread "Thread-5"                   daemon [_thread_in_native, id=23312, stack(0x0000003f9d800000,0x0000003f9d900000) (1024K)]
  0x000002279c5312c0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=18028, stack(0x0000003f9d900000,0x0000003f9da00000) (1024K)]
  0x000002279c532d00 JavaThread "Thread-7"                   daemon [_thread_in_native, id=22832, stack(0x0000003f9da00000,0x0000003f9db00000) (1024K)]
  0x000002279c5340b0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=29432, stack(0x0000003f9db00000,0x0000003f9dc00000) (1024K)]
  0x000002279c531950 JavaThread "Thread-9"                   daemon [_thread_in_native, id=19248, stack(0x0000003f9dc00000,0x0000003f9dd00000) (1024K)]
  0x000002279c52de40 JavaThread "Thread-10"                  daemon [_thread_in_native, id=13244, stack(0x0000003f9dd00000,0x0000003f9de00000) (1024K)]
  0x000002279c52f1f0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=21912, stack(0x0000003f9de00000,0x0000003f9df00000) (1024K)]
  0x000002279c52ff10 JavaThread "Thread-12"                  daemon [_thread_in_native, id=29912, stack(0x0000003f9df00000,0x0000003f9e000000) (1024K)]
  0x000002279c52f880 JavaThread "Thread-13"                  daemon [_thread_in_native, id=10576, stack(0x0000003f9e000000,0x0000003f9e100000) (1024K)]
  0x000002279c5305a0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=23996, stack(0x0000003f9e100000,0x0000003f9e200000) (1024K)]
  0x000002279c533a20 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=18568, stack(0x0000003f9e200000,0x0000003f9e300000) (1024K)]
  0x000002279c530c30 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=14676, stack(0x0000003f9e300000,0x0000003f9e400000) (1024K)]
  0x000002279c534740 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=1528, stack(0x0000003f9e400000,0x0000003f9e500000) (1024K)]
  0x00000227a7a418e0 JavaThread "Cleaner-0"                  daemon [_thread_blocked, id=27540, stack(0x0000003fa0f00000,0x0000003fa1000000) (1024K)]
  0x000002279b9e4350 JavaThread "Worker-7"                          [_thread_blocked, id=27960, stack(0x0000003f9ab00000,0x0000003f9ac00000) (1024K)]
  0x00000227a7a40530 JavaThread "Worker-10"                         [_thread_blocked, id=23580, stack(0x0000003f9c900000,0x0000003f9ca00000) (1024K)]
Total: 39

Other Threads:
  0x0000022793ac0090 VMThread "VM Thread"                           [id=10920, stack(0x0000003f9af00000,0x0000003f9b000000) (1024K)]
  0x000002273acffd90 WatcherThread "VM Periodic Task Thread"        [id=11656, stack(0x0000003f9ae00000,0x0000003f9af00000) (1024K)]
  0x000002273acb1220 WorkerThread "GC Thread#0"                     [id=12528, stack(0x0000003f9ad00000,0x0000003f9ae00000) (1024K)]
  0x0000022799987500 WorkerThread "GC Thread#1"                     [id=7888, stack(0x0000003f9bc00000,0x0000003f9bd00000) (1024K)]
  0x000002279973a240 WorkerThread "GC Thread#2"                     [id=1904, stack(0x0000003f9bd00000,0x0000003f9be00000) (1024K)]
  0x000002279957b720 WorkerThread "GC Thread#3"                     [id=23300, stack(0x0000003f9be00000,0x0000003f9bf00000) (1024K)]
  0x000002279957bac0 WorkerThread "GC Thread#4"                     [id=18096, stack(0x0000003f9bf00000,0x0000003f9c000000) (1024K)]
  0x00000227990ae750 WorkerThread "GC Thread#5"                     [id=29172, stack(0x0000003f9c000000,0x0000003f9c100000) (1024K)]
  0x00000227990aeaf0 WorkerThread "GC Thread#6"                     [id=20496, stack(0x0000003f9c100000,0x0000003f9c200000) (1024K)]
  0x000002279921b6f0 WorkerThread "GC Thread#7"                     [id=24328, stack(0x0000003f9b900000,0x0000003f9ba00000) (1024K)]
  0x000002279921afb0 WorkerThread "GC Thread#8"                     [id=11268, stack(0x0000003f9c600000,0x0000003f9c700000) (1024K)]
  0x000002279921a4d0 WorkerThread "GC Thread#9"                     [id=10848, stack(0x0000003f9c700000,0x0000003f9c800000) (1024K)]
Total: 12

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000022752000000-0x0000022752ba0000-0x0000022752ba0000), size 12189696, SharedBaseAddress: 0x0000022752000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000022753000000-0x0000022793000000, reserved size: 1073741824
Narrow klass base: 0x0000022752000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 5120K, used 2480K [0x00000000d5580000, 0x00000000d5b80000, 0x0000000100000000)
  eden space 4608K, 46% used [0x00000000d5580000,0x00000000d5794218,0x00000000d5a00000)
  from space 512K, 68% used [0x00000000d5a80000,0x00000000d5ad8000,0x00000000d5b00000)
  to   space 512K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5b80000)
 ParOldGen       total 733696K, used 733511K [0x0000000080000000, 0x00000000acc80000, 0x00000000d5580000)
  object space 733696K, 99% used [0x0000000080000000,0x00000000acc51cf8,0x00000000acc80000)
 Metaspace       used 76243K, committed 77952K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K

Card table byte_map: [0x000002273a640000,0x000002273aa50000] _byte_map_base: 0x000002273a240000

Marking Bits: (ParMarkBitMap*) 0x00007ff8384da340
 Begin Bits: [0x000002274d130000, 0x000002274f130000)
 End Bits:   [0x000002274f130000, 0x0000022751130000)

Polling page: 0x0000022738b00000

Metaspace:

Usage:
  Non-class:     66.95 MB used.
      Class:      7.51 MB used.
       Both:     74.46 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      67.88 MB ( 53%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.25 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      76.12 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  12.04 MB
       Class:  7.67 MB
        Both:  19.71 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 124.69 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1512.
num_arena_deaths: 18.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1218.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 29.
num_chunks_taken_from_freelist: 5331.
num_chunk_merges: 15.
num_chunk_splits: 3179.
num_chunks_enlarged: 1701.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=16774Kb max_used=16774Kb free=103225Kb
 bounds [0x0000022745950000, 0x00000227469c0000, 0x000002274ce80000]
CodeHeap 'profiled nmethods': size=120000Kb used=40658Kb max_used=40658Kb free=79341Kb
 bounds [0x000002273de80000, 0x0000022740640000, 0x00000227453b0000]
CodeHeap 'non-nmethods': size=5760Kb used=1486Kb max_used=1596Kb free=4274Kb
 bounds [0x00000227453b0000, 0x0000022745620000, 0x0000022745950000]
CodeCache: size=245760Kb, used=58918Kb, max_used=59028Kb, free=186840Kb
 total_blobs=17341, nmethods=16549, adapters=695, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 1469.919 Thread 0x000002273ad46240 18600       3       java.lang.invoke.LambdaForm$MH/0x0000022753209800::linkToTargetMethod (13 bytes)
Event: 1469.919 Thread 0x00000227a6cc9d10 nmethod 18599 0x00000227469a0d90 code [0x00000227469a0f20, 0x00000227469a1000]
Event: 1469.919 Thread 0x000002273ad46240 nmethod 18600 0x0000022740633090 code [0x0000022740633260, 0x0000022740633518]
Event: 1469.919 Thread 0x000002273ad46240 18601       3       java.lang.invoke.LambdaForm$DMH/0x0000022753209000::newInvokeSpecial (30 bytes)
Event: 1469.919 Thread 0x000002273ad46240 nmethod 18601 0x0000022740633610 code [0x0000022740633800, 0x0000022740633dc8]
Event: 1470.083 Thread 0x000002273ad46240 18602       3       java.lang.Double::doubleToLongBits (16 bytes)
Event: 1470.083 Thread 0x000002273ad46240 nmethod 18602 0x0000022740633f90 code [0x0000022740634140, 0x0000022740634378]
Event: 1470.083 Thread 0x000002273ad46240 18604   !   3       org.eclipse.core.internal.jobs.InternalJob::setAboutToRunCanceled (43 bytes)
Event: 1470.084 Thread 0x000002273ad46240 nmethod 18604 0x0000022740634410 code [0x00000227406345c0, 0x0000022740634828]
Event: 1470.084 Thread 0x000002273ad46240 18605       3       java.lang.Thread::getPriority (17 bytes)
Event: 1470.084 Thread 0x000002273ad46240 nmethod 18605 0x0000022740634910 code [0x0000022740634ac0, 0x0000022740634d88]
Event: 1470.157 Thread 0x000002273ad41fd0 nmethod 18589 0x00000227469a1110 code [0x00000227469a1ca0, 0x00000227469a9550]
Event: 1470.158 Thread 0x000002273ad41fd0 18603       4       java.util.Formatter$FormatSpecifier::print (234 bytes)
Event: 1470.169 Thread 0x000002273ad41fd0 nmethod 18603 0x00000227469afd90 code [0x00000227469b0020, 0x00000227469b0c90]
Event: 1561.867 Thread 0x000002273ad46240 18606       1       org.eclipse.osgi.internal.framework.EquinoxContainer::getStorage (5 bytes)
Event: 1561.867 Thread 0x000002273ad46240 nmethod 18606 0x00000227469b1510 code [0x00000227469b16a0, 0x00000227469b1768]
Event: 1596.823 Thread 0x000002273ad46240 18607       3       java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask::isPeriodic (15 bytes)
Event: 1596.824 Thread 0x000002273ad46240 nmethod 18607 0x0000022740634e90 code [0x0000022740635020, 0x0000022740635188]
Event: 1705.975 Thread 0x000002273ad46240 18608       1       com.sun.jna.internal.Cleaner::access$100 (5 bytes)
Event: 1705.976 Thread 0x000002273ad46240 nmethod 18608 0x00000227469b1810 code [0x00000227469b19a0, 0x00000227469b1a50]

GC Heap History (20 events):
Event: 1140.522 GC heap before
{Heap before GC invocations=638 (full 4):
 PSYoungGen      total 6144K, used 5792K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000d5580000,0x00000000d5a80000,0x00000000d5a80000)
  from space 1024K, 65% used [0x00000000d5a80000,0x00000000d5b28000,0x00000000d5b80000)
  to   space 1024K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5c80000)
 ParOldGen       total 729088K, used 728956K [0x0000000080000000, 0x00000000ac800000, 0x00000000d5580000)
  object space 729088K, 99% used [0x0000000080000000,0x00000000ac7df380,0x00000000ac800000)
 Metaspace       used 76145K, committed 77824K, reserved 1179648K
  class space    used 7686K, committed 8448K, reserved 1048576K
}
Event: 1140.525 GC heap after
{Heap after GC invocations=638 (full 4):
 PSYoungGen      total 6144K, used 416K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a80000)
  from space 1024K, 40% used [0x00000000d5b80000,0x00000000d5be8000,0x00000000d5c80000)
  to   space 1024K, 0% used [0x00000000d5a80000,0x00000000d5a80000,0x00000000d5b80000)
 ParOldGen       total 729600K, used 729532K [0x0000000080000000, 0x00000000ac880000, 0x00000000d5580000)
  object space 729600K, 99% used [0x0000000080000000,0x00000000ac86f3c0,0x00000000ac880000)
 Metaspace       used 76145K, committed 77824K, reserved 1179648K
  class space    used 7686K, committed 8448K, reserved 1048576K
}
Event: 1140.618 GC heap before
{Heap before GC invocations=639 (full 4):
 PSYoungGen      total 6144K, used 5536K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000d5580000,0x00000000d5a80000,0x00000000d5a80000)
  from space 1024K, 40% used [0x00000000d5b80000,0x00000000d5be8000,0x00000000d5c80000)
  to   space 1024K, 0% used [0x00000000d5a80000,0x00000000d5a80000,0x00000000d5b80000)
 ParOldGen       total 729600K, used 729532K [0x0000000080000000, 0x00000000ac880000, 0x00000000d5580000)
  object space 729600K, 99% used [0x0000000080000000,0x00000000ac86f3c0,0x00000000ac880000)
 Metaspace       used 76153K, committed 77824K, reserved 1179648K
  class space    used 7686K, committed 8448K, reserved 1048576K
}
Event: 1140.620 GC heap after
{Heap after GC invocations=639 (full 4):
 PSYoungGen      total 6144K, used 932K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a80000)
  from space 1024K, 91% used [0x00000000d5a80000,0x00000000d5b69210,0x00000000d5b80000)
  to   space 1024K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5c80000)
 ParOldGen       total 730112K, used 729885K [0x0000000080000000, 0x00000000ac900000, 0x00000000d5580000)
  object space 730112K, 99% used [0x0000000080000000,0x00000000ac8c7400,0x00000000ac900000)
 Metaspace       used 76153K, committed 77824K, reserved 1179648K
  class space    used 7686K, committed 8448K, reserved 1048576K
}
Event: 1140.643 GC heap before
{Heap before GC invocations=640 (full 4):
 PSYoungGen      total 6144K, used 6052K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000d5580000,0x00000000d5a80000,0x00000000d5a80000)
  from space 1024K, 91% used [0x00000000d5a80000,0x00000000d5b69210,0x00000000d5b80000)
  to   space 1024K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5c80000)
 ParOldGen       total 730112K, used 729885K [0x0000000080000000, 0x00000000ac900000, 0x00000000d5580000)
  object space 730112K, 99% used [0x0000000080000000,0x00000000ac8c7400,0x00000000ac900000)
 Metaspace       used 76163K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.645 GC heap after
{Heap after GC invocations=640 (full 4):
 PSYoungGen      total 6144K, used 704K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a80000)
  from space 1024K, 68% used [0x00000000d5b80000,0x00000000d5c30000,0x00000000d5c80000)
  to   space 1024K, 0% used [0x00000000d5a80000,0x00000000d5a80000,0x00000000d5b80000)
 ParOldGen       total 731136K, used 730648K [0x0000000080000000, 0x00000000aca00000, 0x00000000d5580000)
  object space 731136K, 99% used [0x0000000080000000,0x00000000ac986200,0x00000000aca00000)
 Metaspace       used 76163K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.663 GC heap before
{Heap before GC invocations=641 (full 4):
 PSYoungGen      total 6144K, used 5824K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000d5580000,0x00000000d5a80000,0x00000000d5a80000)
  from space 1024K, 68% used [0x00000000d5b80000,0x00000000d5c30000,0x00000000d5c80000)
  to   space 1024K, 0% used [0x00000000d5a80000,0x00000000d5a80000,0x00000000d5b80000)
 ParOldGen       total 731136K, used 730648K [0x0000000080000000, 0x00000000aca00000, 0x00000000d5580000)
  object space 731136K, 99% used [0x0000000080000000,0x00000000ac986200,0x00000000aca00000)
 Metaspace       used 76163K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.665 GC heap after
{Heap after GC invocations=641 (full 4):
 PSYoungGen      total 6144K, used 480K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a80000)
  from space 1024K, 46% used [0x00000000d5a80000,0x00000000d5af8000,0x00000000d5b80000)
  to   space 1024K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5c80000)
 ParOldGen       total 731648K, used 731204K [0x0000000080000000, 0x00000000aca80000, 0x00000000d5580000)
  object space 731648K, 99% used [0x0000000080000000,0x00000000aca11230,0x00000000aca80000)
 Metaspace       used 76163K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.691 GC heap before
{Heap before GC invocations=642 (full 4):
 PSYoungGen      total 6144K, used 5600K [0x00000000d5580000, 0x00000000d5c80000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000d5580000,0x00000000d5a80000,0x00000000d5a80000)
  from space 1024K, 46% used [0x00000000d5a80000,0x00000000d5af8000,0x00000000d5b80000)
  to   space 1024K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5c80000)
 ParOldGen       total 731648K, used 731204K [0x0000000080000000, 0x00000000aca80000, 0x00000000d5580000)
  object space 731648K, 99% used [0x0000000080000000,0x00000000aca11230,0x00000000aca80000)
 Metaspace       used 76171K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.694 GC heap after
{Heap after GC invocations=642 (full 4):
 PSYoungGen      total 5632K, used 986K [0x00000000d5580000, 0x00000000d5d80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 96% used [0x00000000d5b80000,0x00000000d5c76900,0x00000000d5c80000)
  to   space 1536K, 0% used [0x00000000d5a00000,0x00000000d5a00000,0x00000000d5b80000)
 ParOldGen       total 731648K, used 731564K [0x0000000080000000, 0x00000000aca80000, 0x00000000d5580000)
  object space 731648K, 99% used [0x0000000080000000,0x00000000aca6b270,0x00000000aca80000)
 Metaspace       used 76171K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.713 GC heap before
{Heap before GC invocations=643 (full 4):
 PSYoungGen      total 5632K, used 5594K [0x00000000d5580000, 0x00000000d5d80000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 96% used [0x00000000d5b80000,0x00000000d5c76900,0x00000000d5c80000)
  to   space 1536K, 0% used [0x00000000d5a00000,0x00000000d5a00000,0x00000000d5b80000)
 ParOldGen       total 731648K, used 731564K [0x0000000080000000, 0x00000000aca80000, 0x00000000d5580000)
  object space 731648K, 99% used [0x0000000080000000,0x00000000aca6b270,0x00000000aca80000)
 Metaspace       used 76173K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.716 GC heap after
{Heap after GC invocations=643 (full 4):
 PSYoungGen      total 5632K, used 608K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 59% used [0x00000000d5a00000,0x00000000d5a98000,0x00000000d5b00000)
  to   space 1024K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5c00000)
 ParOldGen       total 732672K, used 732369K [0x0000000080000000, 0x00000000acb80000, 0x00000000d5580000)
  object space 732672K, 99% used [0x0000000080000000,0x00000000acb34518,0x00000000acb80000)
 Metaspace       used 76173K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.737 GC heap before
{Heap before GC invocations=644 (full 4):
 PSYoungGen      total 5632K, used 5216K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 59% used [0x00000000d5a00000,0x00000000d5a98000,0x00000000d5b00000)
  to   space 1024K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5c00000)
 ParOldGen       total 732672K, used 732369K [0x0000000080000000, 0x00000000acb80000, 0x00000000d5580000)
  object space 732672K, 99% used [0x0000000080000000,0x00000000acb34518,0x00000000acb80000)
 Metaspace       used 76176K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1140.739 GC heap after
{Heap after GC invocations=644 (full 4):
 PSYoungGen      total 5632K, used 512K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 50% used [0x00000000d5b00000,0x00000000d5b80000,0x00000000d5c00000)
  to   space 1024K, 0% used [0x00000000d5a00000,0x00000000d5a00000,0x00000000d5b00000)
 ParOldGen       total 733184K, used 732817K [0x0000000080000000, 0x00000000acc00000, 0x00000000d5580000)
  object space 733184K, 99% used [0x0000000080000000,0x00000000acba4538,0x00000000acc00000)
 Metaspace       used 76176K, committed 77824K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1166.930 GC heap before
{Heap before GC invocations=645 (full 4):
 PSYoungGen      total 5632K, used 5120K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 50% used [0x00000000d5b00000,0x00000000d5b80000,0x00000000d5c00000)
  to   space 1024K, 0% used [0x00000000d5a00000,0x00000000d5a00000,0x00000000d5b00000)
 ParOldGen       total 733184K, used 732817K [0x0000000080000000, 0x00000000acc00000, 0x00000000d5580000)
  object space 733184K, 99% used [0x0000000080000000,0x00000000acba4538,0x00000000acc00000)
 Metaspace       used 76210K, committed 77888K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1166.932 GC heap after
{Heap after GC invocations=645 (full 4):
 PSYoungGen      total 5632K, used 544K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 53% used [0x00000000d5a00000,0x00000000d5a88000,0x00000000d5b00000)
  to   space 1024K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5c00000)
 ParOldGen       total 733184K, used 733145K [0x0000000080000000, 0x00000000acc00000, 0x00000000d5580000)
  object space 733184K, 99% used [0x0000000080000000,0x00000000acbf6578,0x00000000acc00000)
 Metaspace       used 76210K, committed 77888K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1170.068 GC heap before
{Heap before GC invocations=646 (full 4):
 PSYoungGen      total 5632K, used 5152K [0x00000000d5580000, 0x00000000d5c00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 53% used [0x00000000d5a00000,0x00000000d5a88000,0x00000000d5b00000)
  to   space 1024K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5c00000)
 ParOldGen       total 733184K, used 733145K [0x0000000080000000, 0x00000000acc00000, 0x00000000d5580000)
  object space 733184K, 99% used [0x0000000080000000,0x00000000acbf6578,0x00000000acc00000)
 Metaspace       used 76230K, committed 77952K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1170.070 GC heap after
{Heap after GC invocations=646 (full 4):
 PSYoungGen      total 5120K, used 320K [0x00000000d5580000, 0x00000000d5b80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 62% used [0x00000000d5b00000,0x00000000d5b50000,0x00000000d5b80000)
  to   space 512K, 0% used [0x00000000d5a80000,0x00000000d5a80000,0x00000000d5b00000)
 ParOldGen       total 733696K, used 733383K [0x0000000080000000, 0x00000000acc80000, 0x00000000d5580000)
  object space 733696K, 99% used [0x0000000080000000,0x00000000acc31cf8,0x00000000acc80000)
 Metaspace       used 76230K, committed 77952K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1170.138 GC heap before
{Heap before GC invocations=647 (full 4):
 PSYoungGen      total 5120K, used 4928K [0x00000000d5580000, 0x00000000d5b80000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 512K, 62% used [0x00000000d5b00000,0x00000000d5b50000,0x00000000d5b80000)
  to   space 512K, 0% used [0x00000000d5a80000,0x00000000d5a80000,0x00000000d5b00000)
 ParOldGen       total 733696K, used 733383K [0x0000000080000000, 0x00000000acc80000, 0x00000000d5580000)
  object space 733696K, 99% used [0x0000000080000000,0x00000000acc31cf8,0x00000000acc80000)
 Metaspace       used 76235K, committed 77952K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}
Event: 1170.140 GC heap after
{Heap after GC invocations=647 (full 4):
 PSYoungGen      total 5120K, used 352K [0x00000000d5580000, 0x00000000d5b80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 68% used [0x00000000d5a80000,0x00000000d5ad8000,0x00000000d5b00000)
  to   space 512K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5b80000)
 ParOldGen       total 733696K, used 733511K [0x0000000080000000, 0x00000000acc80000, 0x00000000d5580000)
  object space 733696K, 99% used [0x0000000080000000,0x00000000acc51cf8,0x00000000acc80000)
 Metaspace       used 76235K, committed 77952K, reserved 1179648K
  class space    used 7688K, committed 8448K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.013 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.096 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.123 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.130 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.133 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.139 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.163 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.264 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 7.602 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 21.504 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna460705019818861051.dll

Deoptimization events (20 events):
Event: 1166.881 Thread 0x0000022799b92420 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022745a97288 relative=0x0000000000000588
Event: 1166.881 Thread 0x0000022799b92420 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022745a97288 method=org.eclipse.core.internal.jobs.JobManager.isBlocking(Lorg/eclipse/core/internal/jobs/InternalJob;)Z @ 28 c2
Event: 1166.881 Thread 0x0000022799b92420 DEOPT PACKING pc=0x0000022745a97288 sp=0x0000003f9d4fe7e0
Event: 1166.881 Thread 0x0000022799b92420 DEOPT UNPACKING pc=0x0000022745406da2 sp=0x0000003f9d4fe4f0 mode 2
Event: 1166.897 Thread 0x0000022799b92420 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022745a97288 relative=0x0000000000000588
Event: 1166.897 Thread 0x0000022799b92420 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022745a97288 method=org.eclipse.core.internal.jobs.JobManager.isBlocking(Lorg/eclipse/core/internal/jobs/InternalJob;)Z @ 28 c2
Event: 1166.897 Thread 0x0000022799b92420 DEOPT PACKING pc=0x0000022745a97288 sp=0x0000003f9d4fe770
Event: 1166.897 Thread 0x0000022799b92420 DEOPT UNPACKING pc=0x0000022745406da2 sp=0x0000003f9d4fe480 mode 2
Event: 1166.919 Thread 0x0000022799b92420 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022745a9b458 relative=0x0000000000000478
Event: 1166.919 Thread 0x0000022799b92420 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022745a9b458 method=org.eclipse.core.internal.jobs.JobManager.isBlocking(Lorg/eclipse/core/internal/jobs/InternalJob;)Z @ 28 c2
Event: 1166.919 Thread 0x0000022799b92420 DEOPT PACKING pc=0x0000022745a9b458 sp=0x0000003f9d4fe5b0
Event: 1166.919 Thread 0x0000022799b92420 DEOPT UNPACKING pc=0x0000022745406da2 sp=0x0000003f9d4fe310 mode 2
Event: 1166.919 Thread 0x0000022799b92420 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022745df7ad4 relative=0x0000000000000314
Event: 1166.919 Thread 0x0000022799b92420 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022745df7ad4 method=org.eclipse.core.internal.jobs.JobManager.isBlocking(Lorg/eclipse/core/internal/jobs/InternalJob;)Z @ 28 c2
Event: 1166.919 Thread 0x0000022799b92420 DEOPT PACKING pc=0x0000022745df7ad4 sp=0x0000003f9d4fe4c0
Event: 1166.919 Thread 0x0000022799b92420 DEOPT UNPACKING pc=0x0000022745406da2 sp=0x0000003f9d4fe458 mode 2
Event: 1167.003 Thread 0x0000022799b92420 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002274639b04c relative=0x000000000000018c
Event: 1167.003 Thread 0x0000022799b92420 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002274639b04c method=java.io.ObjectOutputStream$HandleTable.lookup(Ljava/lang/Object;)I @ 4 c2
Event: 1167.003 Thread 0x0000022799b92420 DEOPT PACKING pc=0x000002274639b04c sp=0x0000003f9d4fe970
Event: 1167.003 Thread 0x0000022799b92420 DEOPT UNPACKING pc=0x0000022745406da2 sp=0x0000003f9d4fe900 mode 2

Classes loaded (20 events):
Event: 867.629 Loading class java/nio/BufferMismatch
Event: 867.630 Loading class java/nio/BufferMismatch done
Event: 881.353 Loading class sun/nio/fs/WindowsFileCopy
Event: 881.354 Loading class sun/nio/fs/WindowsFileCopy done
Event: 881.556 Loading class jdk/internal/module/IllegalAccessLogger
Event: 881.556 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 921.586 Loading class jdk/internal/module/IllegalAccessLogger
Event: 921.586 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 970.663 Loading class jdk/internal/module/IllegalAccessLogger
Event: 970.663 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 980.461 Loading class jdk/internal/module/IllegalAccessLogger
Event: 980.461 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 995.508 Loading class jdk/internal/module/IllegalAccessLogger
Event: 995.508 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1025.843 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1025.843 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1044.107 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1044.107 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 1085.022 Loading class jdk/internal/module/IllegalAccessLogger
Event: 1085.022 Loading class jdk/internal/module/IllegalAccessLogger done

Classes unloaded (9 events):
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531ab000 'java/lang/invoke/LambdaForm$MH+0x00000227531ab000'
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531aac00 'java/lang/invoke/LambdaForm$MH+0x00000227531aac00'
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531aa800 'java/lang/invoke/LambdaForm$MH+0x00000227531aa800'
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531aa400 'java/lang/invoke/LambdaForm$MH+0x00000227531aa400'
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531aa000 'java/lang/invoke/LambdaForm$BMH+0x00000227531aa000'
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531a9c00 'java/lang/invoke/LambdaForm$DMH+0x00000227531a9c00'
Event: 22.449 Thread 0x0000022793ac0090 Unloading class 0x00000227531a8800 'java/lang/invoke/LambdaForm$DMH+0x00000227531a8800'
Event: 921.367 Thread 0x0000022793ac0090 Unloading class 0x0000022753714c00 'java/lang/invoke/LambdaForm$DMH+0x0000022753714c00'
Event: 921.367 Thread 0x0000022793ac0090 Unloading class 0x0000022753714800 'java/lang/invoke/LambdaForm$DMH+0x0000022753714800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 921.841 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5630310}> (0x00000000d5630310) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 921.899 Thread 0x0000022799b93140 Implicit null exception at 0x0000022745a4080d to 0x0000022745a416ec
Event: 942.923 Thread 0x000002279b9e0ed0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55f2920}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d55f2920) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 970.484 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5738560}> (0x00000000d5738560) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 970.874 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d571e838}> (0x00000000d571e838) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 980.274 Thread 0x0000022799b93140 Implicit null exception at 0x0000022745b9f2d4 to 0x0000022745ba1ae4
Event: 980.299 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57b9780}> (0x00000000d57b9780) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 980.669 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d559b048}> (0x00000000d559b048) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 995.333 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d568ba10}> (0x00000000d568ba10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 995.728 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55c3070}> (0x00000000d55c3070) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1025.663 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d590e518}> (0x00000000d590e518) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1026.057 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56172c8}> (0x00000000d56172c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1043.932 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d578a578}> (0x00000000d578a578) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1044.282 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d8fd68}> (0x00000000d5d8fd68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1084.874 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a69bf8}> (0x00000000d5a69bf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1085.254 Thread 0x0000022799b93140 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5580550}> (0x00000000d5580550) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1167.000 Thread 0x0000022799b92420 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d588e5e8}> (0x00000000d588e5e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1167.000 Thread 0x0000022799b92420 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d588eeb0}> (0x00000000d588eeb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1469.904 Thread 0x000002279b9e4350 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57365f0}> (0x00000000d57365f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1469.904 Thread 0x000002279b9e4350 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5736eb8}> (0x00000000d5736eb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 1383.348 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 1383.348 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 1383.348 Executing VM operation: RendezvousGCThreads
Event: 1383.348 Executing VM operation: RendezvousGCThreads done
Event: 1385.355 Executing VM operation: Cleanup
Event: 1385.356 Executing VM operation: Cleanup done
Event: 1443.557 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 1443.557 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 1443.558 Executing VM operation: RendezvousGCThreads
Event: 1443.558 Executing VM operation: RendezvousGCThreads done
Event: 1470.663 Executing VM operation: Cleanup
Event: 1470.663 Executing VM operation: Cleanup done
Event: 1503.629 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 1503.629 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 1503.629 Executing VM operation: RendezvousGCThreads
Event: 1503.629 Executing VM operation: RendezvousGCThreads done
Event: 1623.830 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 1623.830 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 1623.830 Executing VM operation: RendezvousGCThreads
Event: 1623.830 Executing VM operation: RendezvousGCThreads done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ee57190
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ee62410
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ee7ee10
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ee88910
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ee8a290
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ee96f90
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273eeb9110
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273eebcf10
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273eed3190
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273eed3b10
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273eee3c90
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273eee4d90
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ef3ee10
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ef41a90
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273ef43810
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273efa8410
Event: 921.406 Thread 0x0000022793ac0090 flushing osr nmethod 0x000002273efbb010
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273efeae90
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273f02da90
Event: 921.406 Thread 0x0000022793ac0090 flushing  nmethod 0x000002273f03be10

Events (20 events):
Event: 1320.559 Thread 0x000002279b9dfb20 Thread exited: 0x000002279b9dfb20
Event: 1325.857 Thread 0x000002279b9e3cc0 Thread exited: 0x000002279b9e3cc0
Event: 1325.857 Thread 0x000002279b9e1bf0 Thread exited: 0x000002279b9e1bf0
Event: 1325.857 Thread 0x00000227a7a3d0b0 Thread exited: 0x00000227a7a3d0b0
Event: 1325.858 Thread 0x000002279b9e49e0 Thread exited: 0x000002279b9e49e0
Event: 1325.858 Thread 0x000002279b9e6420 Thread exited: 0x000002279b9e6420
Event: 1325.858 Thread 0x00000227a7a41f70 Thread exited: 0x00000227a7a41f70
Event: 1325.858 Thread 0x000002279b9e3630 Thread exited: 0x000002279b9e3630
Event: 1325.858 Thread 0x00000227a7a42c90 Thread exited: 0x00000227a7a42c90
Event: 1344.065 Thread 0x00000227a7a3fea0 Thread exited: 0x00000227a7a3fea0
Event: 1380.564 Thread 0x000002279b9e0ed0 Thread exited: 0x000002279b9e0ed0
Event: 1384.986 Thread 0x000002279b9e5700 Thread exited: 0x000002279b9e5700
Event: 1440.573 Thread 0x000002279b9e01b0 Thread exited: 0x000002279b9e01b0
Event: 1469.900 Thread 0x000002279b9e4350 Thread added: 0x00000227a7a41f70
Event: 1469.901 Thread 0x00000227a7a41f70 Thread exited: 0x00000227a7a41f70
Event: 1469.905 Thread 0x000002279b9e4350 Thread added: 0x00000227a7a3f810
Event: 1469.905 Thread 0x00000227a7a3f810 Thread exited: 0x00000227a7a3f810
Event: 1469.911 Thread 0x000002273ad46240 Thread added: 0x00000227a6cc9d10
Event: 1470.083 Thread 0x00000227a6cc9d10 Thread exited: 0x00000227a6cc9d10
Event: 1500.575 Thread 0x000002279b9e5070 Thread exited: 0x000002279b9e5070


Dynamic libraries:
0x00007ff76f810000 - 0x00007ff76f81e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ff8d4bd0000 - 0x00007ff8d4de7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8d2d90000 - 0x00007ff8d2e54000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8d20d0000 - 0x00007ff8d24a0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8d2740000 - 0x00007ff8d2851000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8c01c0000 - 0x00007ff8c01d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ff8d4560000 - 0x00007ff8d4711000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8bc400000 - 0x00007ff8bc41e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8d2520000 - 0x00007ff8d2546000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8d2860000 - 0x00007ff8d2889000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8d1d90000 - 0x00007ff8d1eb3000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8baf50000 - 0x00007ff8bb1ec000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ff8d2030000 - 0x00007ff8d20ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8d3b90000 - 0x00007ff8d3c37000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8d42c0000 - 0x00007ff8d42f1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8cbec0000 - 0x00007ff8cbecc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8b6090000 - 0x00007ff8b611d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ff837820000 - 0x00007ff8385b7000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ff8d2890000 - 0x00007ff8d2941000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8d4300000 - 0x00007ff8d43a8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8d1ec0000 - 0x00007ff8d1ee8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8d2c00000 - 0x00007ff8d2d18000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8d47d0000 - 0x00007ff8d4841000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8d0ab0000 - 0x00007ff8d0afd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8c86a0000 - 0x00007ff8c86d4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8ca250000 - 0x00007ff8ca25a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8d0a90000 - 0x00007ff8d0aa3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8d0cf0000 - 0x00007ff8d0d08000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c9820000 - 0x00007ff8c982a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ff8cf180000 - 0x00007ff8cf3b3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8d3f20000 - 0x00007ff8d42b1000 	C:\WINDOWS\System32\combase.dll
0x00007ff8d2950000 - 0x00007ff8d2a28000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8b8c50000 - 0x00007ff8b8c82000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8d26c0000 - 0x00007ff8d273b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8c97a0000 - 0x00007ff8c97af000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ff8bc180000 - 0x00007ff8bc19f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ff8d2e60000 - 0x00007ff8d3701000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8d1ef0000 - 0x00007ff8d202f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff8cfb60000 - 0x00007ff8d047a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8d48c0000 - 0x00007ff8d49ca000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8d3c40000 - 0x00007ff8d3ca9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8d1c00000 - 0x00007ff8d1c25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8ba4b0000 - 0x00007ff8ba4c8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ff8c9640000 - 0x00007ff8c9650000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ff8c9660000 - 0x00007ff8c978c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8d1230000 - 0x00007ff8d1299000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8b8260000 - 0x00007ff8b8276000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ff8c87d0000 - 0x00007ff8c87e0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ff8b1e70000 - 0x00007ff8b1eb4000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ff8d43b0000 - 0x00007ff8d4550000 	C:\WINDOWS\System32\ole32.dll
0x00007ff8d14c0000 - 0x00007ff8d14db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8d0cb0000 - 0x00007ff8d0ce7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff8d1340000 - 0x00007ff8d1368000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8d14e0000 - 0x00007ff8d14ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8d0750000 - 0x00007ff8d077d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff8d3f10000 - 0x00007ff8d3f19000 	C:\WINDOWS\System32\NSI.dll
0x00007ff8b7e30000 - 0x00007ff8b7e79000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna460705019818861051.dll
0x00007ff8d4550000 - 0x00007ff8d4558000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8c97c0000 - 0x00007ff8c97d9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff8c9380000 - 0x00007ff8c939f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-40e5ef75053067ae62606d9243dc3bb7-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 10 days 4:59 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (1703M free)
TotalPageFile size 20259M (AvailPageFile size 7M)
current process WorkingSet (physical memory assigned to process): 1053M, peak: 1057M
current process commit charge ("private bytes"): 1112M, peak: 1125M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
