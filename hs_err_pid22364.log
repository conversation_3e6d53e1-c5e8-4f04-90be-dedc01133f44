#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2097152 bytes for AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=22364, tid=21088
#
# JRE version: Java(TM) SE Runtime Environment (17.0.6+9) (build 17.0.6+9-LTS-190)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\lib\idea_rt.jar=59354:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\bin -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dfile.encoding=UTF-8 com.example.demo.DemoApplication

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Sep 23 22:48:50 2025 SE Asia Standard Time elapsed time: 1928.234016 seconds (0d 0h 32m 8s)

---------------  T H R E A D  ---------------

Current thread (0x00000234d0b77d60):  VMThread "VM Thread" [stack: 0x000000c605c00000,0x000000c605d00000] [id=21088]

Stack: [0x000000c605c00000,0x000000c605d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6734ea]
V  [jvm.dll+0x7d18f4]
V  [jvm.dll+0x7d309e]
V  [jvm.dll+0x7d3703]
V  [jvm.dll+0x2433c5]
V  [jvm.dll+0xa4ae3]
V  [jvm.dll+0x2f1621]
V  [jvm.dll+0x2ed500]
V  [jvm.dll+0x2d5c07]
V  [jvm.dll+0x318417]
V  [jvm.dll+0x7d7f0b]
V  [jvm.dll+0x7d8c44]
V  [jvm.dll+0x7d915d]
V  [jvm.dll+0x7d9534]
V  [jvm.dll+0x7d9600]
V  [jvm.dll+0x781a7a]
V  [jvm.dll+0x672375]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]

VM_Operation (0x000000c6053ff0b0): G1CollectFull, mode: safepoint, requested by thread 0x00000234d65604a0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000234d8c0a380, length=21, elements={
0x00000234d0b7b6f0, 0x00000234d0b7c0a0, 0x00000234d0b8ff10, 0x00000234d0b907d0,
0x00000234d0b920a0, 0x00000234d0b97e70, 0x00000234d0b987e0, 0x00000234d0ba14f0,
0x00000234d1252900, 0x00000234d237b560, 0x00000234d237ba30, 0x00000234d24575e0,
0x00000234d2ce8540, 0x00000234d655c140, 0x00000234d6560e40, 0x00000234d888dbc0,
0x00000234d655f630, 0x00000234d888f3d0, 0x00000234d8f09820, 0x00000234d65604a0,
0x00000234d655cfb0
}

Java Threads: ( => current thread )
  0x00000234d0b7b6f0 JavaThread "Reference Handler" daemon [_thread_blocked, id=22744, stack(0x000000c605d00000,0x000000c605e00000)]
  0x00000234d0b7c0a0 JavaThread "Finalizer" daemon [_thread_blocked, id=12648, stack(0x000000c605e00000,0x000000c605f00000)]
  0x00000234d0b8ff10 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=24492, stack(0x000000c605f00000,0x000000c606000000)]
  0x00000234d0b907d0 JavaThread "Attach Listener" daemon [_thread_blocked, id=27628, stack(0x000000c606000000,0x000000c606100000)]
  0x00000234d0b920a0 JavaThread "Service Thread" daemon [_thread_blocked, id=25968, stack(0x000000c606100000,0x000000c606200000)]
  0x00000234d0b97e70 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=3768, stack(0x000000c606200000,0x000000c606300000)]
  0x00000234d0b987e0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=29604, stack(0x000000c606300000,0x000000c606400000)]
  0x00000234d0ba14f0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=26852, stack(0x000000c606400000,0x000000c606500000)]
  0x00000234d1252900 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=21480, stack(0x000000c606500000,0x000000c606600000)]
  0x00000234d237b560 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=22948, stack(0x000000c606900000,0x000000c606a00000)]
  0x00000234d237ba30 JavaThread "Notification Thread" daemon [_thread_blocked, id=9704, stack(0x000000c606a00000,0x000000c606b00000)]
  0x00000234d24575e0 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=4716, stack(0x000000c606b00000,0x000000c606c00000)]
  0x00000234d2ce8540 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=17424, stack(0x000000c607a00000,0x000000c607b00000)]
  0x00000234d655c140 JavaThread "Live Reload Server" daemon [_thread_in_native, id=25880, stack(0x000000c608500000,0x000000c608600000)]
  0x00000234d6560e40 JavaThread "DestroyJavaVM" [_thread_blocked, id=22356, stack(0x000000c605600000,0x000000c605700000)]
  0x00000234d888dbc0 JavaThread "parallel-1" daemon [_thread_blocked, id=10888, stack(0x000000c605400000,0x000000c605500000)]
  0x00000234d655f630 JavaThread "parallel-2" daemon [_thread_blocked, id=28548, stack(0x000000c606600000,0x000000c606700000)]
  0x00000234d888f3d0 JavaThread "parallel-3" daemon [_thread_blocked, id=13616, stack(0x000000c606800000,0x000000c606900000)]
  0x00000234d8f09820 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=19160, stack(0x000000c607400000,0x000000c607500000)]
  0x00000234d65604a0 JavaThread "Thread-15" [_thread_blocked, id=22236, stack(0x000000c605300000,0x000000c605400000)]
  0x00000234d655cfb0 JavaThread "parallel-4" daemon [_thread_blocked, id=25380, stack(0x000000c607300000,0x000000c607400000)]

Other Threads:
=>0x00000234d0b77d60 VMThread "VM Thread" [stack: 0x000000c605c00000,0x000000c605d00000] [id=21088]
  0x00000234d2466f20 WatcherThread [stack: 0x000000c606c00000,0x000000c606d00000] [id=29276]
  0x00000234b82e9360 GCTaskThread "GC Thread#0" [stack: 0x000000c605700000,0x000000c605800000] [id=18216]
  0x00000234d248ebb0 GCTaskThread "GC Thread#1" [stack: 0x000000c606d00000,0x000000c606e00000] [id=552]
  0x00000234d248ee60 GCTaskThread "GC Thread#2" [stack: 0x000000c606e00000,0x000000c606f00000] [id=29616]
  0x00000234d248f110 GCTaskThread "GC Thread#3" [stack: 0x000000c606f00000,0x000000c607000000] [id=15168]
  0x00000234d25e8d50 GCTaskThread "GC Thread#4" [stack: 0x000000c607000000,0x000000c607100000] [id=14652]
  0x00000234d26a37d0 GCTaskThread "GC Thread#5" [stack: 0x000000c607100000,0x000000c607200000] [id=27556]
  0x00000234d2d492f0 GCTaskThread "GC Thread#6" [stack: 0x000000c607500000,0x000000c607600000] [id=21656]
  0x00000234d2d495a0 GCTaskThread "GC Thread#7" [stack: 0x000000c607600000,0x000000c607700000] [id=18568]
  0x00000234d30e5790 GCTaskThread "GC Thread#8" [stack: 0x000000c607800000,0x000000c607900000] [id=22944]
  0x00000234d32b7e70 GCTaskThread "GC Thread#9" [stack: 0x000000c607900000,0x000000c607a00000] [id=6256]
  0x00000234b82f9e30 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000c605800000,0x000000c605900000] [id=30660]
  0x00000234b82faee0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000c605900000,0x000000c605a00000] [id=25112]
  0x00000234d33fa560 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000c607c00000,0x000000c607d00000] [id=16516]
  0x00000234d33fa810 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000c607d00000,0x000000c607e00000] [id=29508]
  0x00000234d0ab3230 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000c605a00000,0x000000c605b00000] [id=25336]
  0x00000234d27265b0 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000c607200000,0x000000c607300000] [id=26756]
  0x00000234d0ab3b50 ConcurrentGCThread "G1 Service" [stack: 0x000000c605b00000,0x000000c605c00000] [id=2364]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000234b827cc70] Threads_lock - owner thread: 0x00000234d0b77d60
[0x00000234b827b9e0] Heap_lock - owner thread: 0x00000234d65604a0

Heap address: 0x0000000703600000, size: 4042 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bd0000-0x0000000800bd0000), size 12386304, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4042M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 151552K, used 105076K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 31 young (63488K), 3 survivors (6144K)
 Metaspace       used 93204K, committed 93888K, reserved 1130496K
  class space    used 12361K, committed 12672K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703600000| Untracked 
|   1|0x0000000703800000, 0x00000007039fffd8, 0x0000000703a00000| 99%| O|  |TAMS 0x00000007039fffd8, 0x0000000703800000| Untracked 
|   2|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|   3|0x0000000703c00000, 0x0000000703dffff8, 0x0000000703e00000| 99%| O|  |TAMS 0x0000000703dffff8, 0x0000000703c00000| Untracked 
|   4|0x0000000703e00000, 0x0000000703ffffc0, 0x0000000704000000| 99%| O|  |TAMS 0x0000000703ffffc0, 0x0000000703e00000| Untracked 
|   5|0x0000000704000000, 0x00000007041fff68, 0x0000000704200000| 99%| O|  |TAMS 0x00000007041fff68, 0x0000000704000000| Untracked 
|   6|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704200000| Untracked 
|   7|0x0000000704400000, 0x00000007045ffff8, 0x0000000704600000| 99%| O|  |TAMS 0x00000007045ffff8, 0x0000000704400000| Untracked 
|   8|0x0000000704600000, 0x00000007047ffff0, 0x0000000704800000| 99%| O|  |TAMS 0x00000007047ffff0, 0x0000000704600000| Untracked 
|   9|0x0000000704800000, 0x00000007049fffd0, 0x0000000704a00000| 99%| O|  |TAMS 0x00000007049fffd0, 0x0000000704800000| Untracked 
|  10|0x0000000704a00000, 0x0000000704bfffc0, 0x0000000704c00000| 99%| O|  |TAMS 0x0000000704bfffc0, 0x0000000704a00000| Untracked 
|  11|0x0000000704c00000, 0x0000000704dffff8, 0x0000000704e00000| 99%| O|  |TAMS 0x0000000704dffff8, 0x0000000704c00000| Untracked 
|  12|0x0000000704e00000, 0x0000000704fcd848, 0x0000000705000000| 90%| O|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  13|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  14|0x0000000705200000, 0x00000007053ffff8, 0x0000000705400000| 99%| O|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  15|0x0000000705400000, 0x00000007055fffa8, 0x0000000705600000| 99%| O|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  16|0x0000000705600000, 0x00000007057ffff8, 0x0000000705800000| 99%| O|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  17|0x0000000705800000, 0x0000000705902098, 0x0000000705a00000| 50%| O|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  18|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  19|0x0000000705c00000, 0x0000000705c83ed0, 0x0000000705e00000| 25%| O|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  20|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  21|0x0000000706000000, 0x000000070618d258, 0x0000000706200000| 77%| O|  |TAMS 0x0000000706000000, 0x0000000706000000| Untracked 
|  22|0x0000000706200000, 0x0000000706334600, 0x0000000706400000| 60%| O|  |TAMS 0x0000000706200000, 0x0000000706200000| Untracked 
|  23|0x0000000706400000, 0x00000007064f6248, 0x0000000706600000| 48%| O|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  24|0x0000000706600000, 0x00000007066151b0, 0x0000000706800000|  4%| O|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  25|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  26|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  27|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  28|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Untracked 
|  29|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000, 0x0000000707000000| Untracked 
|  30|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000, 0x0000000707200000| Untracked 
|  31|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000, 0x0000000707400000| Untracked 
|  32|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000, 0x0000000707600000| Untracked 
|  33|0x0000000707800000, 0x000000070797d1d0, 0x0000000707a00000| 74%| S|CS|TAMS 0x0000000707800000, 0x0000000707800000| Complete 
|  34|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000, 0x0000000707a00000| Untracked 
|  35|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000, 0x0000000707c00000| Untracked 
|  36|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000, 0x0000000707e00000| Untracked 
|  37|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| S|CS|TAMS 0x0000000708000000, 0x0000000708000000| Complete 
|  38|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| S|CS|TAMS 0x0000000708200000, 0x0000000708200000| Complete 
|  39|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000, 0x0000000708400000| Untracked 
|  40|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000, 0x0000000708600000| Untracked 
|  41|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000, 0x0000000708800000| Untracked 
|  42|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000, 0x0000000708a00000| Untracked 
|  43|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000, 0x0000000708c00000| Untracked 
|  44|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000, 0x0000000708e00000| Untracked 
|  45|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  46|0x0000000709200000, 0x0000000709229770, 0x0000000709400000|  8%| E|  |TAMS 0x0000000709200000, 0x0000000709200000| Complete 
|  47|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| E|CS|TAMS 0x0000000709400000, 0x0000000709400000| Complete 
|  48|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| E|CS|TAMS 0x0000000709600000, 0x0000000709600000| Complete 
|  49|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| E|CS|TAMS 0x0000000709800000, 0x0000000709800000| Complete 
|  50|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| E|CS|TAMS 0x0000000709a00000, 0x0000000709a00000| Complete 
|  51|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| E|CS|TAMS 0x0000000709c00000, 0x0000000709c00000| Complete 
|  52|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| E|CS|TAMS 0x0000000709e00000, 0x0000000709e00000| Complete 
|  53|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| E|CS|TAMS 0x000000070a000000, 0x000000070a000000| Complete 
|  54|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| E|CS|TAMS 0x000000070a200000, 0x000000070a200000| Complete 
|  55|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| E|CS|TAMS 0x000000070a400000, 0x000000070a400000| Complete 
|  56|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| E|CS|TAMS 0x000000070a600000, 0x000000070a600000| Complete 
|  57|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| E|CS|TAMS 0x000000070a800000, 0x000000070a800000| Complete 
|  58|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| E|CS|TAMS 0x000000070aa00000, 0x000000070aa00000| Complete 
|  59|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| E|CS|TAMS 0x000000070ac00000, 0x000000070ac00000| Complete 
|  60|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| E|CS|TAMS 0x000000070ae00000, 0x000000070ae00000| Complete 
|  61|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| E|CS|TAMS 0x000000070b000000, 0x000000070b000000| Complete 
|  62|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| E|CS|TAMS 0x000000070b200000, 0x000000070b200000| Complete 
|  63|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| E|CS|TAMS 0x000000070b400000, 0x000000070b400000| Complete 
|  64|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%| E|CS|TAMS 0x000000070b600000, 0x000000070b600000| Complete 
|  65|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| E|CS|TAMS 0x000000070b800000, 0x000000070b800000| Complete 
|  66|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| E|CS|TAMS 0x000000070ba00000, 0x000000070ba00000| Complete 
|  67|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| E|CS|TAMS 0x000000070bc00000, 0x000000070bc00000| Complete 
|  68|0x000000070be00000, 0x000000070c000000, 0x000000070c000000|100%| E|CS|TAMS 0x000000070be00000, 0x000000070be00000| Complete 
|  69|0x000000070c000000, 0x000000070c200000, 0x000000070c200000|100%| E|CS|TAMS 0x000000070c000000, 0x000000070c000000| Complete 
|  70|0x000000070c200000, 0x000000070c400000, 0x000000070c400000|100%| E|CS|TAMS 0x000000070c200000, 0x000000070c200000| Complete 
| 113|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| E|CS|TAMS 0x0000000711800000, 0x0000000711800000| Complete 
| 114|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| E|CS|TAMS 0x0000000711a00000, 0x0000000711a00000| Complete 
| 115|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| E|CS|TAMS 0x0000000711c00000, 0x0000000711c00000| Complete 

Card table byte_map: [0x00000234c55b0000,0x00000234c5da0000] _byte_map_base: 0x00000234c1d95000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000234b82e9870, (CMBitMap*) 0x00000234b82e98b0
 Prev Bits: [0x00000234c6590000, 0x00000234ca4b8000)
 Next Bits: [0x00000234ca4c0000, 0x00000234ce3e8000)

Polling page: 0x00000234c1290000

Metaspace:

Usage:
  Non-class:     78.95 MB used.
      Class:     12.07 MB used.
       Both:     91.02 MB used.

Virtual space:
  Non-class space:       80.00 MB reserved,      79.31 MB (>99%) committed,  10 nodes.
      Class space:        1.00 GB reserved,      12.38 MB (  1%) committed,  1 nodes.
             Both:        1.08 GB reserved,      91.69 MB (  8%) committed. 

Chunk freelists:
   Non-Class:  300.00 KB
       Class:  3.62 MB
        Both:  3.92 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 150.12 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1764.
num_arena_deaths: 740.
num_vsnodes_births: 11.
num_vsnodes_deaths: 0.
num_space_committed: 1467.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 958.
num_chunks_taken_from_freelist: 5844.
num_chunk_merges: 287.
num_chunk_splits: 3571.
num_chunks_enlarged: 2448.
num_purges: 6.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=34857Kb max_used=35968Kb free=14294Kb
 bounds [0x00000234c12b0000, 0x00000234c35d0000, 0x00000234c42b0000]
 total_blobs=19854 nmethods=19061 adapters=717
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1919.514 Thread 0x00000234d8d898d0 nmethod 19245 0x00000234c35cdf10 code [0x00000234c35ce0a0, 0x00000234c35ce218]
Event: 1919.514 Thread 0x00000234d8cfba10 nmethod 19247 0x00000234c35ce390 code [0x00000234c35ce540, 0x00000234c35ce7f8]
Event: 1919.514 Thread 0x00000234d8f09820 nmethod 19246 0x00000234c35ce990 code [0x00000234c35cebc0, 0x00000234c35cf038]
Event: 1919.514 Thread 0x00000234d0b987e0 nmethod 19248 0x00000234c35cf410 code [0x00000234c35cf640, 0x00000234c35cfcd8]
Event: 1928.221 Thread 0x00000234d8f09820 19249       1       java.security.AccessController::getContext (23 bytes)
Event: 1928.222 Thread 0x00000234d0b987e0 19251   !   1       java.util.concurrent.ThreadPoolExecutor::tryTerminate (163 bytes)
Event: 1928.222 Thread 0x00000234d8f09820 nmethod 19249 0x00000234c33f7310 code [0x00000234c33f74c0, 0x00000234c33f7638]
Event: 1928.222 Thread 0x00000234d8f09820 19252       1       java.util.concurrent.CopyOnWriteArrayList::indexOfRange (63 bytes)
Event: 1928.222 Thread 0x00000234d8f09820 nmethod 19252 0x00000234c33f6d90 code [0x00000234c33f6f40, 0x00000234c33f7148]
Event: 1928.222 Thread 0x00000234d8f09820 19253       1       java.util.ImmutableCollections::listFromArray (39 bytes)
Event: 1928.222 Thread 0x00000234d8f09820 nmethod 19253 0x00000234c33f6690 code [0x00000234c33f6840, 0x00000234c33f6c28]
Event: 1928.223 Thread 0x00000234d0b987e0 nmethod 19251 0x00000234c33f5410 code [0x00000234c33f56c0, 0x00000234c33f5f48]
Event: 1928.224 Thread 0x00000234d0b987e0 19254       1       java.util.regex.Matcher::start (65 bytes)
Event: 1928.224 Thread 0x00000234d8f09820 19255  s!   1       java.net.Socket::close (47 bytes)
Event: 1928.224 Thread 0x00000234d8f09820 nmethod 19255 0x00000234c33f4d90 code [0x00000234c33f4f60, 0x00000234c33f52b8]
Event: 1928.224 Thread 0x00000234d0b987e0 nmethod 19254 0x00000234c33f4410 code [0x00000234c33f4620, 0x00000234c33f4ae8]
Event: 1928.225 Thread 0x00000234d8f09820 19256       1       org.springframework.util.ConcurrentReferenceHashMap$EntryIterator::moveToNextSegment (55 bytes)
Event: 1928.225 Thread 0x00000234d8f09820 nmethod 19256 0x00000234c33f3f90 code [0x00000234c33f4120, 0x00000234c33f4318]
Event: 1928.226 Thread 0x00000234d0b987e0 19257  s    1       org.apache.tomcat.util.modeler.Registry::getRegistry (48 bytes)
Event: 1928.226 Thread 0x00000234d0b987e0 nmethod 19257 0x00000234c33f3910 code [0x00000234c33f3ae0, 0x00000234c33f3e68]

GC Heap History (20 events):
Event: 1751.428 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 155648K, used 123551K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 35 young (71680K), 2 survivors (4096K)
 Metaspace       used 88840K, committed 89792K, reserved 1130496K
  class space    used 11984K, committed 12416K, reserved 1048576K
}
Event: 1751.503 GC heap after
{Heap after GC invocations=30 (full 1):
 garbage-first heap   total 151552K, used 39706K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 88019K, committed 89792K, reserved 1130496K
  class space    used 11864K, committed 12416K, reserved 1048576K
}
Event: 1752.271 GC heap before
{Heap before GC invocations=30 (full 1):
 garbage-first heap   total 151552K, used 125722K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 42 young (86016K), 0 survivors (0K)
 Metaspace       used 88637K, committed 89792K, reserved 1130496K
  class space    used 11935K, committed 12416K, reserved 1048576K
}
Event: 1752.275 GC heap after
{Heap after GC invocations=31 (full 1):
 garbage-first heap   total 151552K, used 45826K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 88637K, committed 89792K, reserved 1130496K
  class space    used 11935K, committed 12416K, reserved 1048576K
}
Event: 1752.970 GC heap before
{Heap before GC invocations=31 (full 1):
 garbage-first heap   total 151552K, used 119554K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 39 young (79872K), 3 survivors (6144K)
 Metaspace       used 89528K, committed 90304K, reserved 1130496K
  class space    used 12028K, committed 12416K, reserved 1048576K
}
Event: 1752.975 GC heap after
{Heap after GC invocations=32 (full 1):
 garbage-first heap   total 151552K, used 51444K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 89528K, committed 90304K, reserved 1130496K
  class space    used 12028K, committed 12416K, reserved 1048576K
}
Event: 1767.225 GC heap before
{Heap before GC invocations=32 (full 1):
 garbage-first heap   total 151552K, used 94452K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 25 young (51200K), 3 survivors (6144K)
 Metaspace       used 89958K, committed 90688K, reserved 1130496K
  class space    used 12070K, committed 12416K, reserved 1048576K
}
Event: 1767.301 GC heap after
{Heap after GC invocations=33 (full 2):
 garbage-first heap   total 151552K, used 42133K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 89744K, committed 90688K, reserved 1130496K
  class space    used 12022K, committed 12416K, reserved 1048576K
}
Event: 1767.948 GC heap before
{Heap before GC invocations=33 (full 2):
 garbage-first heap   total 151552K, used 117909K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 0 survivors (0K)
 Metaspace       used 90291K, committed 91008K, reserved 1130496K
  class space    used 12082K, committed 12416K, reserved 1048576K
}
Event: 1767.952 GC heap after
{Heap after GC invocations=34 (full 2):
 garbage-first heap   total 151552K, used 47106K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 90291K, committed 91008K, reserved 1130496K
  class space    used 12082K, committed 12416K, reserved 1048576K
}
Event: 1768.419 GC heap before
{Heap before GC invocations=34 (full 2):
 garbage-first heap   total 151552K, used 116738K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 3 survivors (6144K)
 Metaspace       used 90857K, committed 91520K, reserved 1130496K
  class space    used 12146K, committed 12480K, reserved 1048576K
}
Event: 1768.423 GC heap after
{Heap after GC invocations=35 (full 2):
 garbage-first heap   total 151552K, used 50792K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 90857K, committed 91520K, reserved 1130496K
  class space    used 12146K, committed 12480K, reserved 1048576K
}
Event: 1891.413 GC heap before
{Heap before GC invocations=35 (full 2):
 garbage-first heap   total 151552K, used 116328K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 5 survivors (10240K)
 Metaspace       used 91490K, committed 92160K, reserved 1130496K
  class space    used 12191K, committed 12544K, reserved 1048576K
}
Event: 1891.418 GC heap after
{Heap after GC invocations=36 (full 2):
 garbage-first heap   total 151552K, used 52448K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 91490K, committed 92160K, reserved 1130496K
  class space    used 12191K, committed 12544K, reserved 1048576K
}
Event: 1917.648 GC heap before
{Heap before GC invocations=36 (full 2):
 garbage-first heap   total 151552K, used 56544K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 4 survivors (8192K)
 Metaspace       used 91493K, committed 92224K, reserved 1130496K
  class space    used 12191K, committed 12544K, reserved 1048576K
}
Event: 1917.726 GC heap after
{Heap after GC invocations=37 (full 3):
 garbage-first heap   total 151552K, used 38831K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 91316K, committed 92224K, reserved 1130496K
  class space    used 12163K, committed 12544K, reserved 1048576K
}
Event: 1918.452 GC heap before
{Heap before GC invocations=37 (full 3):
 garbage-first heap   total 151552K, used 114607K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 0 survivors (0K)
 Metaspace       used 91944K, committed 92672K, reserved 1130496K
  class space    used 12245K, committed 12608K, reserved 1048576K
}
Event: 1918.458 GC heap after
{Heap after GC invocations=38 (full 3):
 garbage-first heap   total 151552K, used 44975K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 91944K, committed 92672K, reserved 1130496K
  class space    used 12245K, committed 12608K, reserved 1048576K
}
Event: 1918.983 GC heap before
{Heap before GC invocations=38 (full 3):
 garbage-first heap   total 151552K, used 116655K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 38 young (77824K), 3 survivors (6144K)
 Metaspace       used 92449K, committed 93120K, reserved 1130496K
  class space    used 12303K, committed 12608K, reserved 1048576K
}
Event: 1918.988 GC heap after
{Heap after GC invocations=39 (full 3):
 garbage-first heap   total 151552K, used 49780K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 92449K, committed 93120K, reserved 1130496K
  class space    used 12303K, committed 12608K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 35.250 Thread 0x00000234d655e7c0 DEOPT PACKING pc=0x00000234c13e40ab sp=0x000000c6086f6260
Event: 35.250 Thread 0x00000234d655e7c0 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6086f5758 mode 3
Event: 35.263 Thread 0x00000234d655e7c0 DEOPT PACKING pc=0x00000234c23a5445 sp=0x000000c6086f6bd0
Event: 35.263 Thread 0x00000234d655e7c0 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6086f60e0 mode 3
Event: 42.838 Thread 0x00000234d6562b20 DEOPT PACKING pc=0x00000234c25ee26a sp=0x000000c608ef9100
Event: 42.838 Thread 0x00000234d6562b20 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c608ef8580 mode 3
Event: 43.242 Thread 0x00000234d655ec90 DEOPT PACKING pc=0x00000234c26cbc08 sp=0x000000c6088febf0
Event: 43.242 Thread 0x00000234d655ec90 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6088fe1a0 mode 3
Event: 56.812 Thread 0x00000234d6562b20 DEOPT PACKING pc=0x00000234c28977cb sp=0x000000c608efb2f0
Event: 56.812 Thread 0x00000234d6562b20 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c608efa818 mode 3
Event: 159.189 Thread 0x00000234d655ec90 DEOPT PACKING pc=0x00000234c14acd43 sp=0x000000c6088fb090
Event: 159.189 Thread 0x00000234d655ec90 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6088fa5b0 mode 3
Event: 170.265 Thread 0x00000234d65617e0 DEOPT PACKING pc=0x00000234c290301f sp=0x000000c608df78c0
Event: 170.265 Thread 0x00000234d65617e0 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c608df6dc8 mode 3
Event: 221.006 Thread 0x00000234d655ec90 DEOPT PACKING pc=0x00000234c2a02c9c sp=0x000000c6088fa220
Event: 221.006 Thread 0x00000234d655ec90 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6088f96d0 mode 0
Event: 224.350 Thread 0x00000234d655e7c0 DEOPT PACKING pc=0x00000234c276f217 sp=0x000000c6086f7290
Event: 224.350 Thread 0x00000234d655e7c0 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6086f6d10 mode 3
Event: 1768.414 Thread 0x00000234d6562b20 DEOPT PACKING pc=0x00000234c32310e4 sp=0x000000c6067fbe40
Event: 1768.414 Thread 0x00000234d6562b20 DEOPT UNPACKING pc=0x00000234c13066e3 sp=0x000000c6067fb2d8 mode 3

Classes unloaded (20 events):
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x00000008014b0800 'jdk/internal/reflect/GeneratedMethodAccessor146'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x0000000801549800 'jdk/internal/reflect/GeneratedMethodAccessor145'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x000000080156c000 'jdk/internal/reflect/GeneratedMethodAccessor144'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x0000000801570000 'jdk/internal/reflect/GeneratedConstructorAccessor105'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x000000080157e800 'jdk/internal/reflect/GeneratedMethodAccessor143'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x000000080157f800 'jdk/internal/reflect/GeneratedMethodAccessor142'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x000000080163cc00 'jdk/internal/reflect/GeneratedMethodAccessor141'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x000000080163d800 'jdk/internal/reflect/GeneratedMethodAccessor140'
Event: 1917.665 Thread 0x00000234d0b77d60 Unloading class 0x00000008016fd000 'jdk/internal/reflect/GeneratedConstructorAccessor103'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x00000008017b5800 'jdk/internal/reflect/GeneratedConstructorAccessor101'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x00000008017eb000 'jdk/internal/reflect/GeneratedConstructorAccessor98'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x00000008017fac00 'jdk/internal/reflect/GeneratedConstructorAccessor97'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x00000008017fb000 'jdk/internal/reflect/GeneratedConstructorAccessor96'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x0000000801801800 'jdk/internal/reflect/GeneratedConstructorAccessor95'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x0000000801802800 'jdk/internal/reflect/GeneratedConstructorAccessor94'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x0000000801806400 'jdk/internal/reflect/GeneratedMethodAccessor139'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x00000008017b9400 'jdk/internal/reflect/GeneratedMethodAccessor77'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x00000008017b4800 'jdk/internal/reflect/GeneratedConstructorAccessor76'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x0000000801580000 'jdk/internal/reflect/GeneratedConstructorAccessor67'
Event: 1917.666 Thread 0x00000234d0b77d60 Unloading class 0x0000000801539c00 'jdk/internal/reflect/GeneratedMethodAccessor44'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1918.347 Thread 0x00000234d888d6f0 Exception <a 'java/io/FileNotFoundException'{0x00000007090c18a0}> (0x00000007090c18a0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 1918.510 Thread 0x00000234d888d6f0 Exception <a 'sun/nio/fs/WindowsException'{0x000000070c193590}> (0x000000070c193590) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 1918.923 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b28d38}> (0x0000000708b28d38) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.926 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b40630}> (0x0000000708b40630) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.926 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b4b2b8}> (0x0000000708b4b2b8) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.927 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b57230}> (0x0000000708b57230) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.928 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b64758}> (0x0000000708b64758) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.929 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b76638}> (0x0000000708b76638) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.929 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708b84668}> (0x0000000708b84668) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.930 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708bc12f8}> (0x0000000708bc12f8) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.931 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708bd0530}> (0x0000000708bd0530) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.932 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708bdef38}> (0x0000000708bdef38) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.933 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708bea468}> (0x0000000708bea468) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.934 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708bf6708}> (0x0000000708bf6708) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.935 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708837db8}> (0x0000000708837db8) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.935 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000708844298}> (0x0000000708844298) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.946 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000007089d4498}> (0x00000007089d4498) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1918.948 Thread 0x00000234d888d6f0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000007089e79b8}> (0x00000007089e79b8) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1919.237 Thread 0x00000234d888d6f0 Exception <a 'sun/nio/fs/WindowsException'{0x000000070acc20d0}> (0x000000070acc20d0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 1928.228 Thread 0x00000234d888e090 Exception <a 'java/lang/InterruptedException'{0x0000000709200040}: sleep interrupted> (0x0000000709200040) 
thrown [s\open\src\hotspot\share\prims\jvm.cpp, line 3070]

VM Operations (20 events):
Event: 1768.414 Executing VM operation: ICBufferFull done
Event: 1768.418 Executing VM operation: G1CollectForAllocation
Event: 1768.423 Executing VM operation: G1CollectForAllocation done
Event: 1769.434 Executing VM operation: Cleanup
Event: 1769.434 Executing VM operation: Cleanup done
Event: 1791.592 Executing VM operation: Cleanup
Event: 1791.592 Executing VM operation: Cleanup done
Event: 1807.702 Executing VM operation: Cleanup
Event: 1807.703 Executing VM operation: Cleanup done
Event: 1891.413 Executing VM operation: G1CollectForAllocation
Event: 1891.418 Executing VM operation: G1CollectForAllocation done
Event: 1917.648 Executing VM operation: G1CollectFull
Event: 1917.727 Executing VM operation: G1CollectFull done
Event: 1918.452 Executing VM operation: G1CollectForAllocation
Event: 1918.458 Executing VM operation: G1CollectForAllocation done
Event: 1918.983 Executing VM operation: G1CollectForAllocation
Event: 1918.988 Executing VM operation: G1CollectForAllocation done
Event: 1919.996 Executing VM operation: Cleanup
Event: 1919.996 Executing VM operation: Cleanup done
Event: 1928.229 Executing VM operation: G1CollectFull

Events (20 events):
Event: 1928.218 Thread 0x00000234d655e2f0 Thread exited: 0x00000234d655e2f0
Event: 1928.218 Thread 0x00000234d655c610 Thread exited: 0x00000234d655c610
Event: 1928.218 Thread 0x00000234d888aba0 Thread exited: 0x00000234d888aba0
Event: 1928.218 Thread 0x00000234d888b070 Thread exited: 0x00000234d888b070
Event: 1928.218 Thread 0x00000234d8890240 Thread exited: 0x00000234d8890240
Event: 1928.218 Thread 0x00000234d888ba10 Thread exited: 0x00000234d888ba10
Event: 1928.218 Thread 0x00000234d888f8a0 Thread exited: 0x00000234d888f8a0
Event: 1928.218 Thread 0x00000234d888a6d0 Thread exited: 0x00000234d888a6d0
Event: 1928.218 Thread 0x00000234d8889390 Thread exited: 0x00000234d8889390
Event: 1928.218 Thread 0x00000234d888fd70 Thread exited: 0x00000234d888fd70
Event: 1928.218 Thread 0x00000234d8890710 Thread exited: 0x00000234d8890710
Event: 1928.218 Thread 0x00000234d8888ec0 Thread exited: 0x00000234d8888ec0
Event: 1928.219 Thread 0x00000234d655cfb0 Thread added: 0x00000234d655cfb0
Event: 1928.222 Thread 0x00000234d6561310 Thread added: 0x00000234d6561310
Event: 1928.222 Thread 0x00000234d888a200 Thread exited: 0x00000234d888a200
Event: 1928.222 Thread 0x00000234d888ef00 Thread exited: 0x00000234d888ef00
Event: 1928.224 Thread 0x00000234d6561310 Thread exited: 0x00000234d6561310
Event: 1928.228 Thread 0x00000234d888e090 Thread exited: 0x00000234d888e090
Event: 1928.228 Thread 0x00000234d888b540 Thread exited: 0x00000234d888b540
Event: 1928.228 Thread 0x00000234d8889d30 Thread exited: 0x00000234d8889d30


Dynamic libraries:
0x00007ff7799a0000 - 0x00007ff7799b0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff8d4bd0000 - 0x00007ff8d4de7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8d2d90000 - 0x00007ff8d2e54000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8d20d0000 - 0x00007ff8d24a0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8d2740000 - 0x00007ff8d2851000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8c3750000 - 0x00007ff8c3768000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff8c3730000 - 0x00007ff8c374b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ff8d2890000 - 0x00007ff8d2941000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8d3b90000 - 0x00007ff8d3c37000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8d4300000 - 0x00007ff8d43a8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8d1ec0000 - 0x00007ff8d1ee8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8d2c00000 - 0x00007ff8d2d18000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8d4560000 - 0x00007ff8d4711000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8baf50000 - 0x00007ff8bb1ec000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ff8d2520000 - 0x00007ff8d2546000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8d2860000 - 0x00007ff8d2889000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8d1d90000 - 0x00007ff8d1eb3000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8d2030000 - 0x00007ff8d20ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8ca250000 - 0x00007ff8ca25a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8d42c0000 - 0x00007ff8d42f1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8cc040000 - 0x00007ff8cc04c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ff8c36a0000 - 0x00007ff8c372e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff8612d0000 - 0x00007ff861ea7000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff8d4550000 - 0x00007ff8d4558000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8c86a0000 - 0x00007ff8c86d4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8c9820000 - 0x00007ff8c9829000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff8d47d0000 - 0x00007ff8d4841000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8d0cf0000 - 0x00007ff8d0d08000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8cb130000 - 0x00007ff8cb13a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff8cf180000 - 0x00007ff8cf3b3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8d3f20000 - 0x00007ff8d42b1000 	C:\WINDOWS\System32\combase.dll
0x00007ff8d2950000 - 0x00007ff8d2a28000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8b8c50000 - 0x00007ff8b8c82000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8d26c0000 - 0x00007ff8d273b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8c3600000 - 0x00007ff8c360e000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ff8c3670000 - 0x00007ff8c3695000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ff87b6b0000 - 0x00007ff87b787000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ff8d2e60000 - 0x00007ff8d3701000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8d1ef0000 - 0x00007ff8d202f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff8cfb60000 - 0x00007ff8d047a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8d48c0000 - 0x00007ff8d49ca000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8d3c40000 - 0x00007ff8d3ca9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8d1c00000 - 0x00007ff8d1c25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8c3610000 - 0x00007ff8c3628000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ff8c3650000 - 0x00007ff8c3669000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ff8c9660000 - 0x00007ff8c978c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8d1230000 - 0x00007ff8d1299000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8c3630000 - 0x00007ff8c3646000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ff8c35e0000 - 0x00007ff8c35fa000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\bin\breakgen64.dll
0x00007ff8d07c0000 - 0x00007ff8d08b8000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ff8d0750000 - 0x00007ff8d077d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff8d3f10000 - 0x00007ff8d3f19000 	C:\WINDOWS\System32\NSI.dll
0x0000000070e10000 - 0x0000000070e36000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007ff8c77a0000 - 0x00007ff8c77aa000 	C:\Windows\System32\rasadhlp.dll
0x00007ff8c8960000 - 0x00007ff8c89e4000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ff8c35d0000 - 0x00007ff8c35da000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007ff8c35c0000 - 0x00007ff8c35cb000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ff8d14c0000 - 0x00007ff8d14db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8d0cb0000 - 0x00007ff8d0ce7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff8d1340000 - 0x00007ff8d1368000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8d14e0000 - 0x00007ff8d14ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8c97c0000 - 0x00007ff8c97d9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff8c9380000 - 0x00007ff8c939f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff89f2e0000 - 0x00007ff89f2f7000 	C:\WINDOWS\system32\napinsp.dll
0x00007ff89f2c0000 - 0x00007ff89f2db000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ff89f170000 - 0x00007ff89f181000 	C:\WINDOWS\System32\winrnr.dll
0x00007ff89f050000 - 0x00007ff89f065000 	C:\WINDOWS\system32\wshbth.dll
0x00007ff89f020000 - 0x00007ff89f041000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ff8c26f0000 - 0x00007ff8c26fd000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ff8d2550000 - 0x00007ff8d26b8000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff8d1660000 - 0x00007ff8d168d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff8d1620000 - 0x00007ff8d1657000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff86c9d0000 - 0x00007ff86c9d8000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;C:\Program Files\Java\jdk-17\bin\server;C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\bin;C:\Program Files\Bonjour

VM Arguments:
jvm_args: -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\lib\idea_rt.jar=59354:C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.4\bin -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dfile.encoding=UTF-8 
java_command: com.example.demo.DemoApplication
java_class_path (initial): C:\Users\<USER>\IdeaProjects\BeeSt0re\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.4.4\spring-boot-starter-web-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.4.4\spring-boot-starter-json-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.3\jackson-datatype-jdk8-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.3\jackson-module-parameter-names-2.18.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.4.4\spring-boot-starter-tomcat-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.39\tomcat-embed-websocket-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.5\spring-web-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.5\spring-beans-6.2.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.14.5\micrometer-observation-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.14.5\micrometer-commons-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.5\spring-webmvc-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.5\spring-context-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.5\spring-expression-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-jasper\10.1.39\tomcat-embed-jasper-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.39\tomcat-embed-core-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\tomcat-annotations-api\10.1.39\tomcat-annotations-api-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embe
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4238344192                                {product} {ergonomic}
   size_t MaxNewSize                               = 2541748224                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4238344192                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 10 days 5:58 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 16163M (2298M free)
TotalPageFile size 20259M (AvailPageFile size 23M)
current process WorkingSet (physical memory assigned to process): 361M, peak: 365M
current process commit charge ("private bytes"): 421M, peak: 427M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190) for windows-amd64 JRE (17.0.6+9-LTS-190), built on Dec  6 2022 15:53:54 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
