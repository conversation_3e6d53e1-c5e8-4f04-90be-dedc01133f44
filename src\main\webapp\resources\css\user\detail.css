.content_detailProduct {
    display: flex;
    margin: 0 auto;
    width: 80%;
    margin-top: 60px;
}

.inf_product,
.img_product {
    width: 50%;
}

.image_shirt {
    width: 80%;
}

.image_shirt_detail {
    width: 25%;
}

.image_shirt_detail {
    transition: transform 0.3s ease, box-shadow 0.3s ease;

}

.image_shirt_detail:hover {
    cursor: pointer;
    transform: scale(1.1);
    border: 1px solid #000;
    border-radius: 10px;
}

.title_inf_products {
    margin-bottom: 20px;
}

.price_inf_products {
    font-weight: 500;

    font-size: 18px;
}

.status_inf_products {
    font-size: 15px;
}

.status_color_inf {
    color: #F05023
}

.color_inf_products {
    margin-top: 20px;
}

.item_box_color {
    width: 20px;
    margin-top: 5px;
    height: 20px;
    margin-bottom: 10px;
}

.size_inf_products {
    margin-top: 10px;
}

.box_option_size {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.item_box_optionSize {
    width: 30px;
    height: 30px;

    display: flex;
    justify-content: center;
    align-items: center;
    border: 0.5px solid #ccc;
    border-radius: 5px;
}

.item_box_optionSize:hover {
    cursor: pointer;
    background-color: #F05023;
    color: #fff;
    font-weight: bold;
    border: none;
}

.size_item_box {
    font-size: 13px;
    font-weight: bold;
}

.quantity_box {
    display: flex;
    justify-content: start;
    gap: 30px;
    align-items: center;
    margin-top: 10px;
}

.quantity_inf_products {
    margin-top: 10px;
}

.detail_quatity {
    display: flex;
    width: 20%;
    height: 40px;
}

.number_quantity {
    width: 60%;
    height: 100%;
}

.quantity {
    width: 30%;
}

.number_quantity {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;

}

.totalProducts {
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;
    width: 45px;
}

.btn_quantity_box {
    padding: 10px 40px;
    border-radius: 5px;
    color: #fff;
    font-weight: bold;
    border: 0.5px solid #F05023;
    background-color: #F05023;
    transition: transform 0.5s ease;
    text-decoration: none;
}

.btn_quantity_box:hover {
    cursor: pointer;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.inf_detailProducts {
    margin-top: 20px;
}

.title_detai_products {
    font-size: 20px;
    margin-bottom: 10px;
}

.box_detail_products_inf>li {
    margin: 5px 0;
    line-height: 25px;
}

.Related_products {
    margin-top: 50px;
}

.product_related {
    display: flex;
    padding: 0 20px;
    justify-content: space-between;
    text-align: center;
    margin-top: 20px;
}

.title_related_products {
    text-align: center;
}

.item_product_related {
    transition: transform 0.5s ease;
    padding: 20px;
    border-radius: 10px;
    width: 20%;
}

.item_product_related:hover {
    cursor: pointer;
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);

}

.boxname_product_related {
    min-height: 40px;
}

.image_products_related {
    width: 100%;
}

/* Product Options Styling */
.product-options {
    margin: 20px 0;
}

.color-section, .size-section {
    margin-bottom: 25px;
}

.option-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: block;
}

/* Color Options */
.color-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.color-option {
    position: relative;
}

.color-radio {
    display: none;
}

.color-label {
    display: block;
    padding: 10px 20px;
    border: 2px solid #ddd;
    border-radius: 25px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    min-width: 80px;
    text-align: center;
}

.color-label:hover {
    border-color: #EE5022;
    background: #fff5f2;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(238, 80, 34, 0.2);
}

.color-radio:checked + .color-label {
    border-color: #EE5022;
    background: linear-gradient(45deg, #EE5022, #ff6b35);
    color: white;
    box-shadow: 0 4px 15px rgba(238, 80, 34, 0.3);
}

.color-name {
    display: block;
}

/* Size Options */
.size-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.size-option {
    position: relative;
}

.size-radio {
    display: none;
}

.size-label {
    display: block;
    padding: 12px 18px;
    border: 2px solid #ddd;
    border-radius: 8px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    min-width: 60px;
    text-align: center;
    position: relative;
}

.size-label:hover:not(.out-of-stock) {
    border-color: #EE5022;
    background: #fff5f2;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(238, 80, 34, 0.2);
}

.size-radio:checked + .size-label {
    border-color: #EE5022;
    background: linear-gradient(45deg, #EE5022, #ff6b35);
    color: white;
    box-shadow: 0 4px 15px rgba(238, 80, 34, 0.3);
}

.size-label.out-of-stock {
    background: #f8f9fa;
    color: #999;
    border-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.size-label.out-of-stock::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 2px;
    background: #dc3545;
    transform: translateY(-50%) rotate(-15deg);
}

.out-of-stock-text {
    display: block;
    font-size: 11px;
    color: #dc3545;
    margin-top: 2px;
}

.size-name {
    display: block;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .color-options, .size-options {
        gap: 8px;
    }

    .color-label, .size-label {
        padding: 8px 15px;
        font-size: 13px;
    }

    .option-label {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .color-options, .size-options {
        gap: 6px;
    }

    .color-label {
        min-width: 70px;
        padding: 8px 12px;
    }

    .size-label {
        min-width: 50px;
        padding: 8px 12px;
    }
}