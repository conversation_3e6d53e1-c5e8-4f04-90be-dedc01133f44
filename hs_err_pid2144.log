#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 266338304 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=2144, tid=11756
#
# JRE version:  (17.0.6+9) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dclassworlds.conf=C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c\bin\m2.conf -Dmaven.home=C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c -Dlibrary.jansi.path=C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c\lib\jansi-native -Dmaven.multiModuleProjectDirectory=C:\Users\<USER>\IdeaProjects\BeeSt0re org.codehaus.plexus.classworlds.launcher.Launcher spring-boot:run

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Sep 23 16:08:16 2025 SE Asia Standard Time elapsed time: 0.013558 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001f406f75a50):  JavaThread "Unknown thread" [_thread_in_vm, id=11756, stack(0x000000b9bf600000,0x000000b9bf700000)]

Stack: [0x000000b9bf600000,0x000000b9bf700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6734ea]
V  [jvm.dll+0x7d18f4]
V  [jvm.dll+0x7d309e]
V  [jvm.dll+0x7d3703]
V  [jvm.dll+0x2433c5]
V  [jvm.dll+0x6703f9]
V  [jvm.dll+0x664d32]
V  [jvm.dll+0x300086]
V  [jvm.dll+0x307606]
V  [jvm.dll+0x356c1e]
V  [jvm.dll+0x356e4f]
V  [jvm.dll+0x2d72e8]
V  [jvm.dll+0x2d8254]
V  [jvm.dll+0x7a33b1]
V  [jvm.dll+0x3647f1]
V  [jvm.dll+0x782839]
V  [jvm.dll+0x3e757f]
V  [jvm.dll+0x3e9001]
C  [jli.dll+0x5297]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff872d9e958, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001f406fe2fa0 GCTaskThread "GC Thread#0" [stack: 0x000000b9bf700000,0x000000b9bf800000] [id=3352]
  0x000001f406ff3b70 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000b9bf800000,0x000000b9bf900000] [id=14512]
  0x000001f406ff4580 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000b9bf900000,0x000000b9bfa00000] [id=24040]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8725d7f07]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001f406f70cf0] Heap_lock - owner thread: 0x000001f406f75a50

Heap address: 0x0000000703600000, size: 4042 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8729bc759]

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (1 events):
Event: 0.006 Loaded shared library C:\Program Files\Java\jdk-17\bin\java.dll


Dynamic libraries:
0x00007ff698e40000 - 0x00007ff698e50000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff8d4bd0000 - 0x00007ff8d4de7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8d2d90000 - 0x00007ff8d2e54000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8d20d0000 - 0x00007ff8d24a0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8d2740000 - 0x00007ff8d2851000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8b9860000 - 0x00007ff8b9878000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff8b8bd0000 - 0x00007ff8b8beb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ff8d2890000 - 0x00007ff8d2941000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8d3b90000 - 0x00007ff8d3c37000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8d4300000 - 0x00007ff8d43a8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8d1ec0000 - 0x00007ff8d1ee8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8d2c00000 - 0x00007ff8d2d18000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8d4560000 - 0x00007ff8d4711000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8d2520000 - 0x00007ff8d2546000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8baf50000 - 0x00007ff8bb1ec000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ff8d2860000 - 0x00007ff8d2889000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8d1d90000 - 0x00007ff8d1eb3000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8d2030000 - 0x00007ff8d20ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8ca250000 - 0x00007ff8ca25a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8d42c0000 - 0x00007ff8d42f1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c9850000 - 0x00007ff8c985c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ff8b7f80000 - 0x00007ff8b800e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff8722f0000 - 0x00007ff872ec7000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff8d4550000 - 0x00007ff8d4558000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8c86a0000 - 0x00007ff8c86d4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8c97a0000 - 0x00007ff8c97a9000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff8d47d0000 - 0x00007ff8d4841000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8d0cf0000 - 0x00007ff8d0d08000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c9640000 - 0x00007ff8c964a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff8cf180000 - 0x00007ff8cf3b3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8d3f20000 - 0x00007ff8d42b1000 	C:\WINDOWS\System32\combase.dll
0x00007ff8d2950000 - 0x00007ff8d2a28000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8b8c50000 - 0x00007ff8b8c82000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8d26c0000 - 0x00007ff8d273b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8b56b0000 - 0x00007ff8b56d5000 	C:\Program Files\Java\jdk-17\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;C:\Program Files\Java\jdk-17\bin\server

VM Arguments:
jvm_args: -Dclassworlds.conf=C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c\bin\m2.conf -Dmaven.home=C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c -Dlibrary.jansi.path=C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c\lib\jansi-native -Dmaven.multiModuleProjectDirectory=C:\Users\<USER>\IdeaProjects\BeeSt0re 
java_command: org.codehaus.plexus.classworlds.launcher.Launcher spring-boot:run
java_class_path (initial): C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.9.9\977a63e90f436cd6ade95b4c0e10c20c\boot\plexus-classworlds-2.8.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4238344192                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4238344192                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=HUY
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 9 days 23:17 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 16163M (2394M free)
TotalPageFile size 20259M (AvailPageFile size 123M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 64M, peak: 318M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190) for windows-amd64 JRE (17.0.6+9-LTS-190), built on Dec  6 2022 15:53:54 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
