/* Global Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    padding: 20px 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 30px;
}

.logo img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 8px;
}

.logo h2 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ecf0f1;
}

.sidebar nav ul {
    list-style: none;
    padding-left: 0;
}

.sidebar nav ul li {
    margin-bottom: 8px;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #ecf0f1;
    text-decoration: none;
    border-radius: 8px;
    transition: background 0.3s ease, transform 0.2s ease;
}

.sidebar nav ul li a i {
    margin-right: 12px;
    font-size: 1rem;
    min-width: 24px;
    text-align: center;
}

.sidebar nav ul li a:hover {
    background: #1abc9c;
    color: #fff;
    transform: translateX(6px);
    box-shadow: 0 4px 8px rgba(26, 188, 156, 0.3);
    border-left: 4px solid #16a085;
}

.sidebar nav ul li.active a {
    background: #1abc9c;
    font-weight: bold;
    color: #fff;
    border-left: 4px solid #16a085;
    box-shadow: 0 4px 8px rgba(26, 188, 156, 0.3);
}

/* Responsive for mobile */
@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }

    .sidebar .logo h2,
    .sidebar nav ul li a span {
        display: none;
    }

    .sidebar nav ul li a {
        justify-content: center;
        padding: 12px 10px;
    }

    .sidebar nav ul li a:hover {
        transform: translateX(2px);
    }

    .main-content {
        margin-left: 90px;
    }
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 270px;
    padding: 20px;
    background: #f5f6fa;
    min-height: 100vh;
}

/* Top Bar */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.search-box {
    display: flex;
    align-items: center;
    background: #f5f6fa;
    padding: 8px 15px;
    border-radius: 20px;
    width: 300px;
}

.search-box i {
    color: #666;
    margin-right: 10px;
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    width: 100%;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notifications {
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.notifications:hover {
    transform: scale(1.1);
}

.notifications.has-notification {
    animation: bellShake 2s infinite;
}

@keyframes bellShake {
    0%, 50%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20%, 40% { transform: rotate(10deg); }
}

.notifications .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e74c3c;
    color: #fff;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    min-width: 18px;
    text-align: center;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: none;
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
}

.notification-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-weight: bold;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.notification-header h6 {
    margin: 0;
    color: #333;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: background 0.2s;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.notification-empty {
    padding: 20px;
    text-align: center;
    color: #999;
    font-style: italic;
}

.notification-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.notification-footer a {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.notification-footer a:hover {
    text-decoration: underline;
}

.notification-time {
    font-size: 0.8rem;
    color: #666;
}

.notification-customer {
    font-size: 0.85rem;
    color: #777;
    margin: 2px 0;
}

.notification-amount {
    font-size: 0.9rem;
    color: #28a745;
    font-weight: 500;
}

.user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
}

/* Dashboard */
.dashboard {
    padding: 20px;
    margin-top: 0;
}

/* Admin Tables */
.dashboard .table-responsive {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.dashboard .table {
    margin-bottom: 0;
}

.dashboard .table th {
    background: #2c3e50 !important;
    color: #fff !important;
    border: none !important;
    font-weight: 600;
    padding: 15px 12px;
}

.dashboard .table td {
    padding: 12px;
    vertical-align: middle;
    border-color: #e9ecef;
}

.dashboard .table tbody tr:hover {
    background-color: #f8f9fa;
}


/* Unified page title */
.page-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 10px 0 20px;
}
.page-title i {
    color: #1abc9c;
}

.dashboard .btn {
    border-radius: 6px;
    font-weight: 500;
}

.dashboard .btn-primary {
    background: #1abc9c;
    border-color: #1abc9c;
}

.dashboard .btn-primary:hover {
    background: #16a085;
    border-color: #16a085;
}

.dashboard h1 {
    margin-bottom: 20px;
    color: #2c3e50;
}

/* Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.card-icon i {
    font-size: 1.5rem;
}

.card-info h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.card-info p {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.trend {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.trend.up {
    color: #2ecc71;
}

.trend.down {
    color: #e74c3c;
}

/* Recent Orders */
.recent-orders {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 1.2rem;
}

.view-all {
    color: #3498db;
    text-decoration: none;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.status.pending {
    background: #fff3e0;
    color: #f57c00;
}

.status.completed {
    background: #e8f5e9;
    color: #2e7d32;
}

.status.shipping {
    background: #e3f2fd;
    color: #1976d2;
}

.btn-view {
    background: #3498db;
    color: #fff;
    border: none;
    padding: 5px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-view:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }

    .sidebar .logo h2,
    .sidebar nav ul li a span {
        display: none;
    }

    .main-content {
        margin-left: 70px;
    }

    .search-box {
        width: 200px;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }
}
.menu {
    position: absolute;
    top: 40px;
    right: 0;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    border-radius: 6px;
    padding: 10px 0;
    z-index: 999;
    min-width: 180px;
    animation: fadeIn 0.2s ease-in-out;
}

.menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu ul li {
    padding: 10px 16px;
    transition: background 0.2s;
}

.menu ul li:hover {
    background-color: #f2f2f2;
}

.menu ul li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
}

/* Lớp để ẩn menu */
.hidden {
    display: none;
}

/* Hiệu ứng nhẹ khi hiển thị menu */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
.user .icon {
    display: flex;
    align-items: center;
    gap: 10px; /* khoảng cách giữa icon và email */
}

.user .icon span {
    font-size: 0.95rem;
    font-weight: 500;
    color: #2c3e50;
    white-space: nowrap; /* tránh bị xuống dòng */
}