package com.example.demo.repository;

import com.example.demo.Entity.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CustomerRepo extends JpaRepository<Customer,Long> {
    List<Customer> findByCodeStartingWithOrderByCodeDesc(String code);

    Customer findByName(String name);

}
